import logging
import re
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime
from enum import Enum

from models.linkedin import LinkedInProfile, TransformedCandidate
from models.comprehensive_candidate import CandidateResponse

logger = logging.getLogger(__name__)


class ValidationSeverity(str, Enum):
    """Severity levels for validation issues."""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class ValidationIssue:
    """Represents a validation issue."""
    
    def __init__(self, field: str, severity: ValidationSeverity, 
                 message: str, suggested_fix: Optional[str] = None):
        self.field = field
        self.severity = severity
        self.message = message
        self.suggested_fix = suggested_fix
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "field": self.field,
            "severity": self.severity,
            "message": self.message,
            "suggested_fix": self.suggested_fix,
            "timestamp": self.timestamp.isoformat()
        }


class SmartHRCandidateValidator:
    """Validator for smartHR candidate data structure."""
    
    def __init__(self):
        self.required_fields = [
            "first_name", "last_name", "email", "phone", "location",
            "current_position", "professional_summary", "experience", 
            "education", "skills"
        ]
        self.field_validators = self._initialize_field_validators()
    
    def _initialize_field_validators(self) -> Dict[str, callable]:
        """Initialize field-specific validators."""
        return {
            "first_name": self._validate_name_field,
            "last_name": self._validate_name_field,
            "email": self._validate_email_field,
            "phone": self._validate_phone_field,
            "location": self._validate_location_field,
            "current_position": self._validate_position_field,
            "professional_summary": self._validate_summary_field,
            "experience": self._validate_experience_array,
            "education": self._validate_education_array,
            "skills": self._validate_skills_array,
            "linkedin_url": self._validate_linkedin_url
        }
    
    def validate_candidate(self, candidate_data: Dict[str, Any]) -> Tuple[bool, List[ValidationIssue]]:
        """Validate complete candidate data structure."""
        issues = []
        
        try:
            # Check required fields
            issues.extend(self._validate_required_fields(candidate_data))
            
            # Validate individual fields
            for field_name, field_value in candidate_data.items():
                if field_name in self.field_validators:
                    field_issues = self.field_validators[field_name](field_value, field_name)
                    issues.extend(field_issues)
            
            # Cross-field validation
            issues.extend(self._validate_cross_field_consistency(candidate_data))
            
            # Data quality checks
            issues.extend(self._validate_data_quality(candidate_data))
            
            # Determine if validation passed
            has_errors = any(issue.severity == ValidationSeverity.ERROR for issue in issues)
            
            return not has_errors, issues
            
        except Exception as e:
            logger.error(f"Candidate validation failed: {str(e)}")
            error_issue = ValidationIssue(
                field="validation_system",
                severity=ValidationSeverity.ERROR,
                message=f"Validation system error: {str(e)}"
            )
            return False, [error_issue]
    
    def _validate_required_fields(self, candidate_data: Dict[str, Any]) -> List[ValidationIssue]:
        """Validate that all required fields are present."""
        issues = []
        
        for field in self.required_fields:
            if field not in candidate_data:
                issues.append(ValidationIssue(
                    field=field,
                    severity=ValidationSeverity.ERROR,
                    message=f"Required field '{field}' is missing",
                    suggested_fix=f"Add '{field}' field to candidate data"
                ))
            elif candidate_data[field] is None:
                issues.append(ValidationIssue(
                    field=field,
                    severity=ValidationSeverity.ERROR,
                    message=f"Required field '{field}' is null",
                    suggested_fix=f"Provide value for '{field}' field"
                ))
        
        return issues
    
    def _validate_name_field(self, value: Any, field_name: str) -> List[ValidationIssue]:
        """Validate name fields (first_name, last_name)."""
        issues = []
        
        if not isinstance(value, str):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message=f"{field_name} must be a string",
                suggested_fix="Convert to string type"
            ))
            return issues
        
        if value == "not_provided":
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message=f"{field_name} is marked as not provided",
                suggested_fix="Try to extract from LinkedIn profile data"
            ))
            return issues
        
        if len(value.strip()) == 0:
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message=f"{field_name} cannot be empty",
                suggested_fix="Provide a valid name"
            ))
        
        if len(value) > 50:
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message=f"{field_name} is unusually long ({len(value)} characters)",
                suggested_fix="Verify name accuracy"
            ))
        
        # Check for valid name characters
        if not re.match(r"^[a-zA-Z\s\-'\.]+$", value):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message=f"{field_name} contains unusual characters",
                suggested_fix="Review name for special characters"
            ))
        
        return issues
    
    def _validate_email_field(self, value: Any, field_name: str) -> List[ValidationIssue]:
        """Validate email field."""
        issues = []
        
        if not isinstance(value, str):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message="Email must be a string"
            ))
            return issues
        
        if value == "not_provided":
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.INFO,
                message="Email not available from LinkedIn data",
                suggested_fix="Consider manual collection or enrichment"
            ))
            return issues
        
        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, value):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message="Invalid email format",
                suggested_fix="Verify email address format"
            ))
        
        return issues
    
    def _validate_phone_field(self, value: Any, field_name: str) -> List[ValidationIssue]:
        """Validate phone field."""
        issues = []
        
        if not isinstance(value, str):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message="Phone must be a string"
            ))
            return issues
        
        if value == "not_provided":
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.INFO,
                message="Phone not available from LinkedIn data",
                suggested_fix="Consider manual collection or enrichment"
            ))
            return issues
        
        # Basic phone validation
        phone_pattern = r'^[\+]?[1-9][\d\s\-\(\)\.]{7,15}$'
        if not re.match(phone_pattern, value):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message="Phone format may be invalid",
                suggested_fix="Verify phone number format"
            ))
        
        return issues
    
    def _validate_location_field(self, value: Any, field_name: str) -> List[ValidationIssue]:
        """Validate location field."""
        issues = []
        
        if not isinstance(value, str):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message="Location must be a string"
            ))
            return issues
        
        if value == "not_provided":
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message="Location not provided",
                suggested_fix="Try to extract from LinkedIn profile"
            ))
            return issues
        
        if len(value.strip()) == 0:
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message="Location is empty"
            ))
        
        return issues
    
    def _validate_position_field(self, value: Any, field_name: str) -> List[ValidationIssue]:
        """Validate current position field."""
        issues = []
        
        if not isinstance(value, str):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message="Current position must be a string"
            ))
            return issues
        
        if value == "not_provided":
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message="Current position not provided",
                suggested_fix="Extract from LinkedIn headline or experience"
            ))
            return issues
        
        if len(value.strip()) == 0:
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message="Current position is empty"
            ))
        
        return issues
    
    def _validate_summary_field(self, value: Any, field_name: str) -> List[ValidationIssue]:
        """Validate professional summary field."""
        issues = []
        
        if not isinstance(value, str):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message="Professional summary must be a string"
            ))
            return issues
        
        if value == "not_provided":
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.INFO,
                message="Professional summary not provided",
                suggested_fix="Generate from LinkedIn headline and experience"
            ))
            return issues
        
        if len(value.strip()) < 20:
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message="Professional summary is very short",
                suggested_fix="Consider expanding with more details"
            ))
        
        return issues
    
    def _validate_experience_array(self, value: Any, field_name: str) -> List[ValidationIssue]:
        """Validate experience array."""
        issues = []
        
        if not isinstance(value, list):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message="Experience must be an array"
            ))
            return issues
        
        if len(value) == 0:
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message="No experience entries found",
                suggested_fix="Extract from LinkedIn experience data"
            ))
            return issues
        
        # Validate individual experience entries
        for i, exp in enumerate(value):
            if not isinstance(exp, dict):
                issues.append(ValidationIssue(
                    field=f"{field_name}[{i}]",
                    severity=ValidationSeverity.ERROR,
                    message="Experience entry must be an object"
                ))
                continue
            
            # Check required experience fields
            required_exp_fields = ["title", "company"]
            for req_field in required_exp_fields:
                if req_field not in exp or not exp[req_field]:
                    issues.append(ValidationIssue(
                        field=f"{field_name}[{i}].{req_field}",
                        severity=ValidationSeverity.WARNING,
                        message=f"Experience entry missing {req_field}"
                    ))
        
        return issues
    
    def _validate_education_array(self, value: Any, field_name: str) -> List[ValidationIssue]:
        """Validate education array."""
        issues = []
        
        if not isinstance(value, list):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message="Education must be an array"
            ))
            return issues
        
        # Education can be empty, so just validate structure if present
        for i, edu in enumerate(value):
            if not isinstance(edu, dict):
                issues.append(ValidationIssue(
                    field=f"{field_name}[{i}]",
                    severity=ValidationSeverity.ERROR,
                    message="Education entry must be an object"
                ))
                continue
            
            if "school" not in edu or not edu["school"]:
                issues.append(ValidationIssue(
                    field=f"{field_name}[{i}].school",
                    severity=ValidationSeverity.WARNING,
                    message="Education entry missing school name"
                ))
        
        return issues
    
    def _validate_skills_array(self, value: Any, field_name: str) -> List[ValidationIssue]:
        """Validate skills array."""
        issues = []
        
        if not isinstance(value, list):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message="Skills must be an array"
            ))
            return issues
        
        if len(value) == 0:
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message="No skills found",
                suggested_fix="Extract from LinkedIn skills data"
            ))
            return issues
        
        # Check for valid skill entries
        for i, skill in enumerate(value):
            if not isinstance(skill, str):
                issues.append(ValidationIssue(
                    field=f"{field_name}[{i}]",
                    severity=ValidationSeverity.ERROR,
                    message="Skill must be a string"
                ))
            elif len(skill.strip()) == 0:
                issues.append(ValidationIssue(
                    field=f"{field_name}[{i}]",
                    severity=ValidationSeverity.WARNING,
                    message="Empty skill entry"
                ))
        
        return issues
    
    def _validate_linkedin_url(self, value: Any, field_name: str) -> List[ValidationIssue]:
        """Validate LinkedIn URL."""
        issues = []
        
        if not isinstance(value, str):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.ERROR,
                message="LinkedIn URL must be a string"
            ))
            return issues
        
        if value == "not_provided":
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.INFO,
                message="LinkedIn URL not available"
            ))
            return issues
        
        # Validate LinkedIn URL format
        linkedin_pattern = r'^https://www\.linkedin\.com/in/[a-zA-Z0-9\-]+/?$'
        if not re.match(linkedin_pattern, value):
            issues.append(ValidationIssue(
                field=field_name,
                severity=ValidationSeverity.WARNING,
                message="Invalid LinkedIn URL format",
                suggested_fix="Verify LinkedIn profile URL"
            ))
        
        return issues
    
    def _validate_cross_field_consistency(self, candidate_data: Dict[str, Any]) -> List[ValidationIssue]:
        """Validate consistency across fields."""
        issues = []
        
        # Check if current position matches latest experience
        current_position = candidate_data.get("current_position", "")
        experience = candidate_data.get("experience", [])
        
        if (current_position and current_position != "not_provided" 
            and experience and len(experience) > 0):
            latest_exp = experience[0]  # Assuming first is latest
            if isinstance(latest_exp, dict) and latest_exp.get("is_current"):
                exp_title = latest_exp.get("title", "")
                if exp_title and exp_title.lower() != current_position.lower():
                    issues.append(ValidationIssue(
                        field="current_position",
                        severity=ValidationSeverity.INFO,
                        message="Current position doesn't match latest experience title",
                        suggested_fix="Verify position consistency"
                    ))
        
        return issues
    
    def _validate_data_quality(self, candidate_data: Dict[str, Any]) -> List[ValidationIssue]:
        """Validate overall data quality."""
        issues = []
        
        # Count "not_provided" fields
        not_provided_count = sum(
            1 for value in candidate_data.values() 
            if isinstance(value, str) and value == "not_provided"
        )
        
        if not_provided_count > 3:
            issues.append(ValidationIssue(
                field="data_completeness",
                severity=ValidationSeverity.WARNING,
                message=f"Many fields marked as not provided ({not_provided_count})",
                suggested_fix="Consider data enrichment strategies"
            ))
        
        return issues


# Convenience functions
def validate_smarthr_candidate(candidate_data: Dict[str, Any]) -> Tuple[bool, List[ValidationIssue]]:
    """Validate smartHR candidate data."""
    validator = SmartHRCandidateValidator()
    return validator.validate_candidate(candidate_data)


def validate_transformed_candidate(transformed_candidate: TransformedCandidate) -> Tuple[bool, List[ValidationIssue]]:
    """Validate transformed candidate object."""
    return validate_smarthr_candidate(transformed_candidate.candidate_info)


def validate_comprehensive_candidate(candidate_response: CandidateResponse) -> Tuple[bool, List[ValidationIssue]]:
    """Validate comprehensive candidate response structure."""
    issues = []

    try:
        # Validate personal info
        if not candidate_response.personal_info.full_name:
            issues.append(ValidationIssue(
                field="personal_info.full_name",
                severity=ValidationSeverity.ERROR,
                message="Full name is required",
                suggested_fix="Ensure full name is extracted from LinkedIn profile"
            ))

        # Validate work experience structure
        for i, exp in enumerate(candidate_response.work_experience):
            if not exp.job_title:
                issues.append(ValidationIssue(
                    field=f"work_experience[{i}].job_title",
                    severity=ValidationSeverity.WARNING,
                    message="Job title is missing",
                    suggested_fix="Extract job title from LinkedIn experience"
                ))

            if not exp.company_name:
                issues.append(ValidationIssue(
                    field=f"work_experience[{i}].company_name",
                    severity=ValidationSeverity.WARNING,
                    message="Company name is missing",
                    suggested_fix="Extract company name from LinkedIn experience"
                ))

        # Validate education structure
        for i, edu in enumerate(candidate_response.education):
            if not edu.institution_name:
                issues.append(ValidationIssue(
                    field=f"education[{i}].institution_name",
                    severity=ValidationSeverity.WARNING,
                    message="Institution name is missing",
                    suggested_fix="Extract institution name from LinkedIn education"
                ))

        # Validate skills structure
        for i, skill in enumerate(candidate_response.skills):
            if not skill.name:
                issues.append(ValidationIssue(
                    field=f"skills[{i}].name",
                    severity=ValidationSeverity.WARNING,
                    message="Skill name is missing",
                    suggested_fix="Ensure skill names are properly extracted"
                ))

        # Check data completeness
        missing_sections = []
        if not candidate_response.work_experience:
            missing_sections.append("work_experience")
        if not candidate_response.skills:
            missing_sections.append("skills")
        if not candidate_response.education:
            missing_sections.append("education")

        if missing_sections:
            issues.append(ValidationIssue(
                field="data_completeness",
                severity=ValidationSeverity.INFO,
                message=f"Missing sections: {', '.join(missing_sections)}",
                suggested_fix="Consider data enrichment or manual review"
            ))

        # Determine if validation passed
        has_errors = any(issue.severity == ValidationSeverity.ERROR for issue in issues)

        return not has_errors, issues

    except Exception as e:
        logger.error(f"Comprehensive candidate validation failed: {str(e)}")
        error_issue = ValidationIssue(
            field="validation_system",
            severity=ValidationSeverity.ERROR,
            message=f"Validation system error: {str(e)}"
        )
        return False, [error_issue]
