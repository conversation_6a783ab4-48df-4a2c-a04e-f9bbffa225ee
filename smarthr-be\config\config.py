# config.py
import os

DATABASE_CONFIG = {
    'user': os.getenv('DB_USER', 'default_user'),
    'password': os.getenv('DB_PASSWORD', 'default_password'),
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 5432)),
    'database': os.getenv('DB_NAME', 'postgres'),
}

CANDIDATE_RESUME_PAGE_OPTIONS = {
    'page-size': 'A4',
    'margin-top': '0.25in',
    'margin-right': '0.75in',
    'margin-bottom': '0.75in',
    'margin-left': '0.75in',
    'encoding': "UTF-8",
    'no-outline': None
}

MODELS_CONFIG = {
    # Default order prioritizing GPT-5 series with robust fallbacks
    'default_models_order': eval(os.getenv('DEFAULT_MODELS_ORDER', '["gpt-5", "gpt-5-mini", "gpt-4o", "gpt-4o-mini", "llama4-pro", "llama4-light"]')),

    # Position matching optimized for cost-efficiency with GPT-5 mini
    'position_matching_models_order': eval(os.getenv('POSITION_MATCH_MODELS_ORDER', '["gpt-5-mini", "gpt-5", "gpt-4o-mini", "gpt-4o", "llama4-light", "llama4-pro"]')),

    # Interview evaluation prioritizing reasoning capabilities
    'interview_evaluation_models_order': eval(os.getenv('INTERVIEW_EVAL_MODELS_ORDER', '["gpt-5", "gpt-5-large", "gpt-4o", "llama4-pro", "gpt-5-mini"]')),

    # LinkedIn transformation optimized for speed and cost
    'linkedin_transformation_models_order': eval(os.getenv('LINKEDIN_TRANSFORM_MODELS_ORDER', '["gpt-5-mini", "gpt-4o-mini", "gpt-5", "llama4-light"]')),

    # Experience-based reranking configuration
    # High experience weight (80%) ensures that role-specific experience dominates
    # the final percentage calculation shown in the frontend
    'experience_reranking': {
        'enabled': os.getenv('EXPERIENCE_RERANKING_ENABLED', 'true').lower() == 'true',
        'experience_weight': float(os.getenv('EXPERIENCE_RERANKING_WEIGHT', '0.8')),  # 80% - previous experience is primary
        'similarity_weight': float(os.getenv('SIMILARITY_RERANKING_WEIGHT', '0.2')),  # 20% - vector similarity is secondary
        'models_order': eval(os.getenv('RERANKING_MODELS_ORDER', '["gpt-5-mini", "gpt-5", "gpt-4o-mini", "gpt-4o", "llama4-light", "llama4-pro"]')),
        'timeout_seconds': int(os.getenv('RERANKING_TIMEOUT_SECONDS', '300')),
        'fallback_on_error': os.getenv('RERANKING_FALLBACK_ON_ERROR', 'true').lower() == 'true'
    }
}
