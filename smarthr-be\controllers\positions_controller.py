# positions_controller.py
from contextlib import contextmanager
from datetime import datetime, time
import threading
from typing import List, Optional
from fastapi import HTT<PERSON><PERSON>x<PERSON>
import psycopg2
from psycopg2 import sql
from psycopg2.extras import Json
from config.config import MODELS_CONFIG
from core.config import settings
from models.llm import inference_with_fallback, get_related_class_definitions 
from models.SpecalizedOutputFormats.positionOutput import Position as OutputFormatted
from models.SpecalizedOutputFormats.postionSkills import TopSkills
from langchain_core.messages import HumanMessage
from models.models import Position, PositionCreate, PositionFilters, PositionRawCreate, PositionUpdate
from services.match_service import generate_match_for_position
from templates.positions_templates.positions.json_column import (
    prepare_position_for_embedding,
)
from utils.embedding_utils import clean_sparse_embedding, format_vector, generate_openai_embedding, get_embeddings


# DB helper to get cursor
@contextmanager
def get_cursor():
    conn = psycopg2.connect(settings.DATABASE_URL, connect_timeout=120, options="-c statement_timeout=120000", keepalives_idle=30)  # 120 seconds timeout
    try:
        with conn:
            with conn.cursor() as cur:
                yield cur
    finally:
        conn.close()


# Create a new position in the database
def create_position(position_data: PositionCreate) -> Position:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    embed_text = prepare_position_for_embedding(
        position_data.proj_id, position_data.position_info
    )

    sparse_embedding, dense_embedding = get_embeddings(str(position_data.position_info))
    embedding_vector = format_vector(dense_embedding) if dense_embedding else None
    sparse_dict = clean_sparse_embedding(sparse_embedding)

    cur.execute(
        """
            INSERT INTO positions_smarthr (proj_id, position_info, top_candidates, to_be_embebbed, embedding, sparse_embedding, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
            RETURNING id, proj_id, position_info, top_candidates, last_matching, created_at, updated_at
        """,
        (
            position_data.proj_id,
            Json(position_data.position_info),
            Json(position_data.top_candidates),
            embed_text,
            embedding_vector,
            Json(sparse_dict),
        ),
    )
    row = cur.fetchone()
    conn.commit()
    cur.close()
    conn.close()

    # Generate match for position in a different thread
    threading.Thread(target=generate_match_for_position, args=(str(row[0]), row[5], row[6])).start()
    # generate_match_for_position(str(row[0]), row[5], row[5])

    return Position(
        id=str(row[0]),
        proj_id=str(row[1]),
        position_info=row[2],
        top_candidates=row[3],
        last_matching=row[4],
        created_at=row[5],
        updated_at=row[6],
    )


# Update an existing position in the database
def update_position(position_data: PositionUpdate) -> Position:
    print("Updating position:", position_data)
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    embed_text = prepare_position_for_embedding(
        position_data.proj_id, position_data.position_info
    )

    sparse_embedding, dense_embedding = get_embeddings(str(position_data.position_info))
    embedding_vector = format_vector(dense_embedding) if dense_embedding else None
    sparse_dict = clean_sparse_embedding(sparse_embedding)
    params = [Json(position_data.position_info), Json(position_data.top_candidates)]

    sqlQuery = """
        UPDATE positions_smarthr
        SET position_info = %s,
            top_candidates = %s,
            to_be_embebbed = %s,
            embedding = %s,
            sparse_embedding = %s,
            updated_at = NOW()
        WHERE id::text=%s
        RETURNING id, proj_id, position_info, top_candidates, last_matching, created_at, updated_at
    """

    params.extend([embed_text, embedding_vector, Json(sparse_dict), position_data.id])

    cur.execute(sqlQuery, params)
    row = cur.fetchone()
    conn.commit()
    cur.close()
    conn.close()

    # Generate match for position in a different thread
    threading.Thread(target=generate_match_for_position, args=(str(row[0]), row[5], row[6])).start()
    # generate_match_for_position(str(row[0]), row[5], row[5])

    return Position(
        id=str(row[0]),
        proj_id=str(row[1]),
        position_info=row[2],
        top_candidates=row[3],
        last_matching=row[4],
        created_at=row[5],
        updated_at=row[6],
    )


# Create a new position from raw data
def create_position_from_raw(positionOriginal: PositionRawCreate):

    position_raw_text = positionOriginal.position_description
    # Implement ingestion from raw datasource
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    print("=================Checkpoint=============")
    model_text_schema = """
class Skill(BaseModel):
    skillName: Optional[str] = Field(..., description="Name of the skill (e.g., Azure Data Factory).")
    skillCategory: Optional[str] = Field(..., description="Category of the skill (e.g., professionalSkills, niceToHave).")
    skillLeveName: Optional[str] = Field(..., description="Skill level name (e.g., Intermediate, Advanced).")
    skillScore: Optional[int] = Field(..., description="Numerical score representing the skill proficiency (e.g., 1-5).")

class PositionAllocation(BaseModel):
    Name: Optional[str] = Field(..., description="Name of the country or region associated with the position.")
    isoCode: Optional[str] = Field(None, description="ISO code of the country or region (e.g., US, IN).")

class ReasonStatus(BaseModel):
    reason: Optional[str] = Field(None, description="Reason why the position was closed or updated.")

class Seniority(BaseModel):
    name: Optional[str] = Field(..., description="Seniority level of the position (e.g., Junior, Senior).")

class Position(BaseModel):
    positionName: Optional[str] = Field(..., description="Name of the position (e.g., Azure Data Engineer).")
    clientName: Optional[str] = Field("", description="Name of the client offering the position.")
    jobDescription: Optional[str] = Field("", description="Detailed job description in plain text.")
    mainResponsabilities: Optional[str] = Field("", description="Main responsibilities of the position.")
    seniority: Seniority = Field(..., description="Seniority level of the position.")
    roleName: Optional[str] = Field("", description="Role associated with the position (e.g., Data Engineer).")
    projectName: Optional[str] = Field("", description="Project name the position is associated with.")
    positionTypeName: Optional[str] = Field("", description="Type of position (e.g., Full-time, Contract).")
    positionCreateDate: Optional[str] = Field("", description="Date when the position was created.")
    positionStartDate: Optional[str] = Field("", description="Start date of the position.")
    positionCloseDate: Optional[str] = Field("", description="Close date of the position, if applicable.")
    createdBy: Optional[str] = Field("", description="Name or identifier of the person who created the position.")
    reasonStatus: Optional[ReasonStatus] = Field(None, description="Reason for the current status of the position.")
    openPositionSkills: Optional[List[Skill]] = Field(
        None, description="List of skills required for the position. Return this as a JSON array of objects."
    )
    positionAllocations: Optional[List[PositionAllocation]] = Field(
        None, description="List of geographic allocations for the position. Return this as a JSON array of objects."
    )    
    
"""

    def transform_position_to_json(position):
        text_model_schema = get_related_class_definitions(OutputFormatted)
        try:
            resp = inference_with_fallback(
                task_prompt="""
                    Extract the information from a position and ensure the output is fully JSON-compliant.
                    Clean the position data, as it might contain symbols or unwanted formatting.

                    INSTRUCTIONS:
                    • Sanitize the position data to remove symbols, unwanted formatting, or irrelevant characters.
                    • Ensure the output is a valid JSON object with correct structure and syntax.

                    IMPORTANT REQUIREMENTS:
                    • Include only the locations explicitly mentioned in the position (e.g., under PositionAllocations or similar context).
                    • Do not fabricate or infer additional locations.
                    • Do not add extra skills; only include those specified in the position content.
                    • Extract the locations found in the field `positionAllocations`, using the `Name` attribute. These represent valid deployment regions (e.g., "LATAM", "India", etc.).
                    • Do not fabricate or infer locations that are not explicitly defined in the `positionAllocations` field.
                    • Do not include extra PositionAllocations or skills not present in the original data.
                    """,
                model_schema=OutputFormatted,
                user_messages=[HumanMessage(content=position)],
                model_schema_text=model_text_schema,
                models_order=MODELS_CONFIG["default_models_order"]
            )
        except:
            raise Exception("Could not extract")

        if resp is None:
            raise Exception("No suitable model available")
        resp = resp.model_dump()
        return resp

    position_info = transform_position_to_json(position_raw_text)  # Json position transformed

    def transform_position_to_embedeed_text(position):
        resp = inference_with_fallback(
            task_prompt="The following position should be cleaned up and separated for a next embedding part. Extract the most possible for a good matching with candidates",
            model_schema=None,
            user_messages=[HumanMessage(content=position)],
            model_schema_text=None,
            models_order=MODELS_CONFIG["default_models_order"]
        )
        return resp.content

    embed_text = transform_position_to_embedeed_text(position_raw_text)
    print("=================Checkpoint Embed Text=============", embed_text)
    embedding = generate_openai_embedding(embed_text)
    embedding_vector = format_vector(embedding) if embedding else None

    ## Priority skills
    def extract_position_skills(position_to_be_extracted):
        resp = inference_with_fallback(
            task_prompt="Extract the skills mentioned in a position considering the JSON description.",
            model_schema=TopSkills,
            user_messages=[HumanMessage(content=position_to_be_extracted)],
            model_schema_text=get_related_class_definitions(TopSkills),
            models_order=MODELS_CONFIG["default_models_order"]
        )
        return resp.model_dump()
    priority_skills = extract_position_skills(position_raw_text)

    current_position = None
    if positionOriginal.external_id is not None:        
        current_position = get_position_by_external_id(positionOriginal.proj_id, positionOriginal.external_id)

    if current_position is None:
        cur.execute(
            """
                INSERT INTO positions_smarthr (proj_id, position_info, top_candidates, to_be_embebbed, embedding, created_at, updated_at, priority_skills, external_id)
                VALUES (%s, %s, %s, %s, %s, NOW(), NOW(), %s, %s)
                RETURNING id, proj_id, position_info, top_candidates, last_matching, created_at, updated_at, priority_skills
            """,
            (
                positionOriginal.proj_id,
                Json(position_info),
                Json([]),
                embed_text,
                embedding_vector, 
                Json(priority_skills),
                positionOriginal.external_id,
            ),
        )
    else:
        cur.execute(
            """
                UPDATE positions_smarthr
                SET position_info = %s, top_candidates = %s, to_be_embebbed = %s, embedding = %s, updated_at = NOW(), priority_skills = %s
                WHERE external_id::text = %s AND proj_id::text = %s and id = %s
                RETURNING id, proj_id, position_info, top_candidates, last_matching, created_at, updated_at, priority_skills
            """,
            (
                Json(position_info),
                Json([]),
                embed_text,
                embedding_vector,
                Json(priority_skills),
                positionOriginal.external_id,
                positionOriginal.proj_id,
                current_position.id
            ),
        )
    row = cur.fetchone()
    conn.commit()
    cur.close()
    conn.close()

    # Generate match for position in a different thread
    threading.Thread(target=generate_match_for_position, args=(str(row[0]), row[5], row[6])).start()
    # generate_match_for_position(str(row[0]), row[5], row[5])

    return Position(
        id=str(row[0]),
        proj_id=str(row[1]),
        position_info=row[2],
        top_candidates=row[3],
        last_matching=row[4],
        created_at=row[5],
        updated_at=row[6],
    )


# Get a position by its ID
def get_position_by_id(position_id: str) -> Optional[Position]:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    cur.execute(
        """
        SELECT id, proj_id, position_info, top_candidates, last_matching, created_at, updated_at
        FROM positions_smarthr WHERE id::text=%s
    """,
        (position_id,),
    )
    row = cur.fetchone()
    cur.close()
    conn.close()
    if not row:
        return None
    return Position(
        id=str(row[0]),
        proj_id=str(row[1]),
        position_info=row[2],
        top_candidates=row[3],
        last_matching=row[4],
        created_at=row[5],
        updated_at=row[6],
    )


# Get a position by its external ID
def get_position_by_external_id(project_id: str, external_id: str) -> Optional[Position]:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    cur.execute(
        """
        SELECT id, proj_id, position_info, top_candidates, last_matching, created_at, updated_at
        FROM positions_smarthr WHERE external_id::text=%s AND proj_id::text=%s
    """,
        (external_id, project_id),
    )
    row = cur.fetchone()
    cur.close()
    conn.close()
    if not row:
        return None
    return Position(
        id=str(row[0]),
        proj_id=str(row[1]),
        position_info=row[2],
        top_candidates=row[3],
        last_matching=row[4],
        created_at=row[5],
        updated_at=row[6],
    )


# Fetch all positions from the database
def fetch_all_positions() -> List[Position]:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    cur.execute(
        """
        SELECT id, proj_id, position_info, top_candidates, last_matching, created_at, updated_at
        FROM positions_smarthr
        ORDER BY created_at DESC
    """
    )
    rows = cur.fetchall()
    cur.close()
    conn.close()
    positions = []
    for row in rows:
        positions.append(
            Position(
                id=str(row[0]),
                proj_id=str(row[1]),
                position_info=row[2],
                top_candidates=row[3],
                last_matching=row[4],
                created_at=row[5],
                updated_at=row[6],
            )
        )
    return positions


# Fetch all positions by project ID
def fetch_all_positions_by_project_id(project_id: str) -> List[Position]:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    cur.execute(
        """
        SELECT id, proj_id, position_info, top_candidates, last_matching, created_at, updated_at
        FROM positions_smarthr WHERE proj_id::text=%s
        ORDER BY created_at DESC
    """,
        (project_id,),)

    rows = cur.fetchall()
    cur.close()
    conn.close()
    positions = []
    for row in rows:
        positions.append(
            Position(
                id=str(row[0]),
                proj_id=str(row[1]),
                position_info=row[2],
                top_candidates=row[3],
                last_matching=row[4],
                created_at=row[5],
                updated_at=row[6],
            )
        )
    return positions


# Get positions with pagination and optional search term
def get_positions_page(page: int, chunk_size: int = 20, filters: PositionFilters | None = None):
    if page < 1:
        raise HTTPException(status_code=400, detail="Page number must be greater than or equal to 1.")
        # raise ValueError("Page number must be greater than or equal to 1.")

    if chunk_size < 1 or chunk_size > 1000:
        raise HTTPException(status_code=400, detail="Chunk size must be between 1 and 1000.")
        # raise ValueError("Chunk size must be between 1 and 1000.")

    params = []
    offset = (page - 1) * chunk_size
    if offset < 0:
        # raise ValueError("Offset cannot be negative.")
        raise HTTPException(status_code=400, detail="Offset cannot be negative.")

    query = sql.SQL("""
          SELECT id, proj_id, position_info, top_candidates, last_matching, created_at, updated_at
        FROM positions_smarthr
         {where_clause}
        ORDER BY created_at DESC
        LIMIT %s OFFSET %s
    """)

    where_clause, params = build_where_clause(filters)
    params.append(chunk_size)
    params.append(offset)
    query = query.format(where_clause=where_clause)

    positions = []

    try:
        with get_cursor() as cur:
            cur.execute(query, params)
            rows = cur.fetchall()
            for row in rows:
                positions.append(
                    Position(
                        id=str(row[0]),
                        proj_id=str(row[1]),
                        position_info=row[2],
                        top_candidates=row[3],
                        last_matching=row[4],
                        created_at=row[5],
                        updated_at=row[6],
                    )
                )
    except Exception as e:
        raise e

    return positions


# Get the total number of positions, optionally filtered by a search term
def get_total_positions(filters: PositionFilters | None = None) -> int:
    params = []
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    query = sql.SQL("""SELECT COUNT(*) FROM positions_smarthr {where_clause}""")

    where_clause = sql.SQL("")

    # if (len(search_term) > 0):
    #     where_clause = sql.SQL("WHERE lower(position_info::text) LIKE %s")
    #     params.insert(0, f"%{search_term.lower()}%")
    where_clause, params = build_where_clause(filters)
    query = query.format(where_clause=where_clause)

    cur.execute(query, params)

    row = cur.fetchone()
    cur.close()
    conn.close()
    if not row:
        return 0

    return row[0]


# Delete a position by its ID
def delete_position(position_id: str) -> bool:
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        cur.execute(
            """
            DELETE FROM positions_smarthr WHERE id::text=%s
        """,
            (position_id,),
        )
        conn.commit()
        cur.close()
        conn.close()
        return True
    except psycopg2.Error as e:
        print(f"Error deleting position: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error deleting position: {e}")
        return False


# Build the WHERE clause for the SQL query based on the filters provided
def build_where_clause(filters: PositionFilters) -> sql.SQL:

    where_clause = sql.SQL("WHERE 1=1")  # Start with a true condition
    params = []

    if filters:
        # Check if filters are provided and build the WHERE clause accordingly
        if (len(filters.search_term.strip()) > 0):
            where_clause += sql.SQL(" AND lower(position_info::text) LIKE %s")
            params.append(f"%{filters.search_term.lower()}%")

        if filters.stage is not None:
            if filters.stage is True:
                where_clause += sql.SQL(" AND position_info -> 'reasonStatus' IS NULL OR position_info -> 'reasonStatus' ->> 'reason' IS NULL OR TRIM(position_info -> 'reasonStatus' ->> 'reason') = ''")
            else:
                where_clause += sql.SQL(" AND position_info -> 'reasonStatus' IS NOT NULL AND TRIM(position_info -> 'reasonStatus' ->> 'reason') <> ''")  # No positions in this stage

        if filters.client_name:
            # Assuming client_name is a string and we want exact match
            where_clause += sql.SQL(" AND lower(position_info #>> '{clientName}') = lower(%s)")
            params.append(filters.client_name)

        if filters.location:
            where_clause += sql.SQL(" AND lower(position_info #>> '{positionAllocations}') LIKE %s")
            params.append(f"%{filters.location.lower()}%")

        # Date filters
        # Assuming created_from and created_to are datetime objects
        if filters.created_from and filters.created_to:
            # Normalize created_from to 00:00:00
            start = datetime.combine(filters.created_from.date(), time.min)
            # Normalize created_to to 23:59:59.999999
            end = datetime.combine(filters.created_to.date(), time.max)
            where_clause += sql.SQL(" AND created_at BETWEEN %s AND %s")
            params.append(start)
            params.append(end)
    return where_clause, params


# Get distinct locations from the positions table
# This function retrieves unique location names from the position allocations in the database.
def get_locations() -> List[str]:
    with get_cursor() as cur:
        cur.execute("""
            SELECT DISTINCT alloc->>'Name' AS location
            FROM public.positions_smarthr,
            LATERAL jsonb_array_elements(position_info->'positionAllocations') AS alloc
            WHERE alloc->>'Name' IS NOT NULL AND alloc->>'Name' <> '' order by location asc
        """)
        rows = cur.fetchall()
    return [row[0] for row in rows]


# Get distinct clients from the positions table
# This function retrieves unique client names from the position information in the database.
def get_clients() -> List[str]:
    with get_cursor() as cur:
        cur.execute("""
            SELECT DISTINCT position_info->>'clientName' AS client_name
            FROM public.positions_smarthr
            WHERE position_info->>'clientName' IS NOT NULL AND position_info->>'clientName' <> '' order by client_name asc
        """)
        rows = cur.fetchall()
    return [row[0] for row in rows]


# Get all positions with fetch_all_positions
# Update each positions with update_position
def update_all_positions():
    positions = fetch_all_positions()
    updated_positions = []
    for position in positions:
        updated_position = update_position(PositionUpdate(
            id=position.id,
            proj_id=position.proj_id,
            position_info=position.position_info,
            top_candidates=position.top_candidates,
            question_answers=position.question_answers
        ))
        updated_positions.append(updated_position)
    return updated_positions
