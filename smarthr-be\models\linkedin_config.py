from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, field_validator
from enum import Enum
import os


class LinkedInAPIProvider(str, Enum):
    """Supported LinkedIn API providers."""
    LINKEDIN_OFFICIAL = "linkedin_official"
    LINKEDIN_SCRAPER = "linkedin_scraper"
    THIRD_PARTY_API = "third_party_api"
    MOCK_PROVIDER = "mock_provider"


class RateLimitConfig(BaseModel):
    """Rate limiting configuration for LinkedIn API calls."""
    requests_per_minute: int = Field(default=30, ge=1, le=1000)
    requests_per_hour: int = Field(default=500, ge=1, le=10000)
    requests_per_day: int = Field(default=5000, ge=1, le=100000)
    burst_limit: int = Field(default=10, ge=1, le=100)
    backoff_factor: float = Field(default=2.0, ge=1.0, le=10.0)
    max_retries: int = Field(default=3, ge=0, le=10)
    timeout_seconds: int = Field(default=30, ge=5, le=300)


class LinkedInCredentials(BaseModel):
    """LinkedIn API credentials configuration."""
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    api_key: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    session_cookies: Optional[Dict[str, str]] = None
    
    @field_validator('client_id', 'client_secret', 'access_token', mode='before')
    @classmethod
    def load_from_env(cls, v, info):
        if v is None:
            field_name = info.field_name
            env_var = f"LINKEDIN_{field_name.upper()}"
            return os.getenv(env_var)
        return v


class LinkedInAPIConfig(BaseModel):
    """Complete LinkedIn API configuration."""
    provider: LinkedInAPIProvider = LinkedInAPIProvider.MOCK_PROVIDER
    credentials: LinkedInCredentials = LinkedInCredentials()
    rate_limits: RateLimitConfig = RateLimitConfig()
    base_url: str = "https://www.linkedin.com"

    def __init__(self, **data):
        # Allow environment variable override for provider
        if 'provider' not in data:
            env_provider = os.getenv('LINKEDIN_API_PROVIDER', 'mock_provider')
            try:
                # Since use_enum_values = True, store the string value
                data['provider'] = env_provider
            except ValueError:
                # If invalid provider in env, use default
                data['provider'] = 'mock_provider'
        super().__init__(**data)
    api_version: str = "v2"
    user_agent: str = "smartHR-LinkedIn-Integration/1.0"
    enable_caching: bool = True
    cache_ttl_seconds: int = 3600
    enable_logging: bool = True
    log_level: str = "INFO"
    
    # Search-specific configuration
    default_search_limit: int = Field(default=25, ge=1, le=100)
    max_search_limit: int = Field(default=100, ge=1, le=1000)
    enable_profile_enrichment: bool = True
    profile_fields: List[str] = [
        "id", "firstName", "lastName", "headline", "summary", 
        "location", "industry", "positions", "educations", "skills"
    ]
    
    # Proxy configuration
    use_proxy: bool = False
    proxy_url: Optional[str] = None
    proxy_rotation: bool = False
    
    class Config:
        use_enum_values = True


class SchemaTransformationConfig(BaseModel):
    """Configuration for schema transformation operations."""
    llm_models_order: List[str] = [
        "gpt-4o", "gpt-4o-mini", "llama4-pro", "llama4-light"
    ]
    transformation_temperature: float = Field(default=0.1, ge=0.0, le=2.0)
    max_transformation_retries: int = Field(default=3, ge=1, le=10)
    validation_enabled: bool = True
    strict_validation: bool = False

    # Fallback configuration
    use_fallback_transformation: bool = True
    confidence_threshold: float = Field(default=0.7, ge=0.0, le=1.0)

    # Field mapping configuration
    required_smarthr_fields: List[str] = [
        "first_name", "last_name", "email", "phone", "location"
    ]
    optional_smarthr_fields: List[str] = [
        "headline", "summary", "experience", "education", "skills"
    ]

    # Transformation rules
    field_mapping_rules: Dict[str, str] = {
        "firstName": "first_name",
        "lastName": "last_name",
        "headline": "current_position",
        "summary": "professional_summary",
        "location.name": "location",
        "positions": "experience",
        "educations": "education",
        "skills": "skills"
    }

    # Data enrichment
    enable_data_enrichment: bool = True
    infer_missing_fields: bool = True
    generate_candidate_summary: bool = True
    extract_key_skills: bool = True

    # Quality control
    minimum_confidence_score: float = Field(default=0.7, ge=0.0, le=1.0)
    flag_low_confidence: bool = True
    require_manual_review: bool = False


class AgentConfig(BaseModel):
    """Configuration for LinkedIn integration agents."""
    agent_timeout_seconds: int = Field(default=120, ge=30, le=600)
    enable_parallel_processing: bool = True
    max_concurrent_requests: int = Field(default=5, ge=1, le=20)
    
    # Agent 1 (LinkedIn Search) specific config
    search_agent_config: Dict[str, Any] = {
        "enable_profile_caching": True,
        "cache_duration_hours": 24,
        "enable_search_optimization": True,
        "auto_retry_failed_searches": True
    }
    
    # Agent 2 (Schema Transformation) specific config
    transformation_agent_config: Dict[str, Any] = {
        "enable_batch_processing": True,
        "batch_size": 10,
        "enable_transformation_caching": True,
        "validate_output_schema": True
    }


class LinkedInIntegrationConfig(BaseModel):
    """Master configuration for LinkedIn integration system."""
    api_config: LinkedInAPIConfig = LinkedInAPIConfig()
    transformation_config: SchemaTransformationConfig = SchemaTransformationConfig()
    agent_config: AgentConfig = AgentConfig()
    
    # System-wide settings
    enable_monitoring: bool = True
    enable_metrics: bool = True
    enable_health_checks: bool = True
    health_check_interval_seconds: int = 300
    
    # Error handling
    enable_error_recovery: bool = True
    max_system_retries: int = Field(default=3, ge=1, le=10)
    fallback_to_manual_mode: bool = True
    
    # Performance settings
    enable_async_processing: bool = True
    request_timeout_seconds: int = Field(default=60, ge=10, le=300)
    connection_pool_size: int = Field(default=10, ge=1, le=50)
    
    # Security settings
    enable_request_validation: bool = True
    enable_response_sanitization: bool = True
    log_sensitive_data: bool = False
    
    @classmethod
    def load_from_env(cls) -> 'LinkedInIntegrationConfig':
        """Load configuration from environment variables."""
        return cls()
    
    def validate_config(self) -> List[str]:
        """Validate the configuration and return any issues."""
        issues = []
        
        # Check credentials
        if self.api_config.provider != LinkedInAPIProvider.MOCK_PROVIDER:
            creds = self.api_config.credentials
            if not any([creds.client_id, creds.access_token, creds.api_key]):
                issues.append("No valid LinkedIn credentials configured")
        
        # Check rate limits
        rate_limits = self.api_config.rate_limits
        if rate_limits.requests_per_minute > rate_limits.requests_per_hour:
            issues.append("Requests per minute cannot exceed requests per hour")
        
        # Check transformation config
        if self.transformation_config.minimum_confidence_score > 1.0:
            issues.append("Minimum confidence score cannot exceed 1.0")
        
        return issues


# Default configuration instance
DEFAULT_LINKEDIN_CONFIG = LinkedInIntegrationConfig()


# Configuration validation functions
def validate_linkedin_credentials(credentials: LinkedInCredentials) -> bool:
    """Validate LinkedIn credentials."""
    return any([
        credentials.client_id and credentials.client_secret,
        credentials.access_token,
        credentials.api_key,
        credentials.username and credentials.password
    ])


def get_active_linkedin_config() -> LinkedInIntegrationConfig:
    """Get the active LinkedIn configuration."""
    config = LinkedInIntegrationConfig.load_from_env()
    issues = config.validate_config()

    if issues:
        raise ValueError(f"LinkedIn configuration issues: {', '.join(issues)}")

    return config


def load_linkedin_config() -> LinkedInIntegrationConfig:
    """Load LinkedIn configuration from environment variables."""
    return LinkedInIntegrationConfig.load_from_env()
