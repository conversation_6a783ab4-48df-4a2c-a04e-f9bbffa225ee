import logging
import async<PERSON>
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from datetime import datetime
import json

from models.linkedin import (
    LinkedInSearchRequest,
    LinkedInSearchResponse,
    LinkedInSearchFilters,
    SchemaTransformationRequest,
    SchemaTransformationResponse,
    LinkedInProfile,
    TransformedCandidate,
    AgentError
)
from controllers.linkedin_agents import LinkedInSearchAgent, SchemaTransformationAgent
from models.linkedin_config import LinkedInIntegrationConfig

logger = logging.getLogger(__name__)


class LinkedInAgentCommunicationError(Exception):
    """Exception for agent communication errors."""
    pass


class LinkedInAgentOrchestrator:
    """Orchestrates communication between LinkedIn agents."""
    
    def __init__(self, config: LinkedInIntegrationConfig):
        self.config = config
        self.search_agent = LinkedInSearchAgent(config)
        self.transformation_agent = SchemaTransformationAgent(config)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def search_and_transform_candidates(
        self,
        search_request: LinkedInSearchRequest,
        transform_immediately: bool = True
    ) -> Tuple[LinkedInSearchResponse, Optional[SchemaTransformationResponse]]:
        """
        Orchestrate the complete LinkedIn search and transformation pipeline.
        
        Args:
            search_request: LinkedIn search parameters
            transform_immediately: Whether to transform results immediately
            
        Returns:
            Tuple of (search_response, transformation_response)
        """
        try:
            self.logger.info(f"Starting LinkedIn search and transformation pipeline")
            
            # Step 1: Execute LinkedIn search
            search_response = await self._execute_search(search_request)
            
            if not search_response.success:
                self.logger.error(f"LinkedIn search failed: {search_response.error_message}")
                return search_response, None
            
            if not search_response.profiles:
                self.logger.info("No profiles found in search results")
                return search_response, None
            
            # Step 2: Transform profiles if requested
            transformation_response = None
            if transform_immediately:
                transformation_response = await self._execute_transformation(search_response.profiles)
                
                if not transformation_response.success:
                    self.logger.warning(f"Transformation failed: {transformation_response.error_message}")
            
            self.logger.info(
                f"Pipeline completed: {len(search_response.profiles)} profiles found, "
                f"{len(transformation_response.transformed_candidates) if transformation_response else 0} transformed"
            )
            
            return search_response, transformation_response
            
        except Exception as e:
            self.logger.error(f"Agent orchestration failed: {str(e)}")
            raise LinkedInAgentCommunicationError(f"Pipeline execution failed: {str(e)}")
    
    async def _execute_search(self, search_request: LinkedInSearchRequest) -> LinkedInSearchResponse:
        """Execute LinkedIn search through Search Agent."""
        try:
            self.logger.info(f"Executing LinkedIn search with {len(search_request.filters.keywords)} keywords")
            
            # Process search request through Search Agent
            from models.linkedin import LinkedInAgent1Request
            agent_request = LinkedInAgent1Request(search_request=search_request)
            agent_response = await self.search_agent.process_request(agent_request)

            # Create a new search response with success information
            search_response_data = agent_response.search_results.model_dump()
            search_response_data['success'] = agent_response.success
            search_response_data['error_message'] = agent_response.error_message
            search_response = LinkedInSearchResponse(**search_response_data)
            
            # Log search results
            if search_response.success:
                self.logger.info(
                    f"Search completed successfully: {search_response.total_results} total results, "
                    f"{len(search_response.profiles)} profiles returned"
                )
            else:
                self.logger.error(f"Search failed: {search_response.error_message}")
            
            return search_response
            
        except Exception as e:
            self.logger.error(f"Search execution failed: {str(e)}")
            return LinkedInSearchResponse(
                search_id=f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                profiles=[],
                total_results=0,
                returned_results=0,
                search_filters_used=search_request.filters,
                search_metadata={"error": str(e)},
                execution_time_ms=0,
                success=False,
                error_message=f"Search execution error: {str(e)}"
            )
    
    async def _execute_transformation(self, profiles: List[LinkedInProfile]) -> SchemaTransformationResponse:
        """Execute schema transformation through Transformation Agent."""
        try:
            self.logger.info(f"Executing schema transformation for {len(profiles)} profiles")
            
            # Create transformation request
            transformation_request = SchemaTransformationRequest(
                linkedin_profiles=profiles,
                target_schema="smarthr_candidate",
                transformation_options={
                    "use_llm_enhancement": True,
                    "validate_output": True,
                    "include_confidence_scores": True
                }
            )
            
            # Process transformation request through Transformation Agent
            transformation_response = await self.transformation_agent.process_request(transformation_request)
            
            # Log transformation results
            if transformation_response.success:
                self.logger.info(
                    f"Transformation completed successfully: {len(transformation_response.transformed_candidates)} candidates transformed"
                )
                
                # Log confidence statistics
                if transformation_response.transformed_candidates:
                    confidences = [c.transformation_confidence for c in transformation_response.transformed_candidates]
                    avg_confidence = sum(confidences) / len(confidences)
                    self.logger.info(f"Average transformation confidence: {avg_confidence:.2f}")
            else:
                self.logger.error(f"Transformation failed: {transformation_response.error_message}")
            
            return transformation_response
            
        except Exception as e:
            self.logger.error(f"Transformation execution failed: {str(e)}")
            return SchemaTransformationResponse(
                transformed_candidates=[],
                success=False,
                error_message=f"Transformation execution error: {str(e)}",
                processing_time_ms=0
            )
    
    async def batch_search_and_transform(
        self,
        search_requests: List[LinkedInSearchRequest],
        max_concurrent: int = 3
    ) -> List[Tuple[LinkedInSearchResponse, Optional[SchemaTransformationResponse]]]:
        """
        Execute multiple search and transformation pipelines concurrently.
        
        Args:
            search_requests: List of search requests to process
            max_concurrent: Maximum number of concurrent operations
            
        Returns:
            List of (search_response, transformation_response) tuples
        """
        try:
            self.logger.info(f"Starting batch processing of {len(search_requests)} search requests")
            
            # Create semaphore to limit concurrent operations
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_single_request(request: LinkedInSearchRequest):
                async with semaphore:
                    return await self.search_and_transform_candidates(request)
            
            # Execute all requests concurrently
            results = await asyncio.gather(
                *[process_single_request(request) for request in search_requests],
                return_exceptions=True
            )
            
            # Process results and handle exceptions
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"Batch request {i} failed: {str(result)}")
                    # Create error response
                    error_search_response = LinkedInSearchResponse(
                        search_id=f"batch_error_{i}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        profiles=[],
                        total_results=0,
                        returned_results=0,
                        search_filters_used=search_requests[i].filters if i < len(search_requests) else LinkedInSearchFilters(),
                        search_metadata={"batch_index": i, "error": str(result)},
                        execution_time_ms=0,
                        success=False,
                        error_message=str(result)
                    )
                    processed_results.append((error_search_response, None))
                else:
                    processed_results.append(result)
            
            successful_results = sum(1 for search_resp, _ in processed_results if search_resp.success)
            self.logger.info(f"Batch processing completed: {successful_results}/{len(search_requests)} successful")
            
            return processed_results
            
        except Exception as e:
            self.logger.error(f"Batch processing failed: {str(e)}")
            raise LinkedInAgentCommunicationError(f"Batch processing error: {str(e)}")
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status information for both agents."""
        try:
            return {
                "search_agent": {
                    "class": self.search_agent.__class__.__name__,
                    "config": {
                        "api_provider": self.config.api_config.provider,
                        "rate_limit": self.config.api_config.rate_limits.requests_per_minute
                    }
                },
                "transformation_agent": {
                    "class": self.transformation_agent.__class__.__name__,
                    "config": {
                        "llm_models": self.config.transformation_config.llm_models_order,
                        "use_fallback": self.config.transformation_config.use_fallback_transformation
                    }
                },
                "orchestrator": {
                    "status": "ready",
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Status check failed: {str(e)}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }


class LinkedInAgentCommunicationProtocol:
    """Protocol for standardized agent communication."""
    
    @staticmethod
    def create_search_request(
        keywords: List[str],
        location: Optional[str] = None,
        experience_level: Optional[str] = None,
        skills: Optional[List[str]] = None,
        school: Optional[str] = None,
        limit: int = 25
    ) -> LinkedInSearchRequest:
        """Create a standardized LinkedIn search request."""
        from models.linkedin import LinkedInSearchFilters

        filters = LinkedInSearchFilters(
            keywords=keywords,
            location=location,
            experience_level=experience_level,
            skills=skills,
            school=school
        )
        
        return LinkedInSearchRequest(
            filters=filters,
            limit=limit,
            search_metadata={
                "created_at": datetime.now().isoformat(),
                "protocol_version": "1.0"
            }
        )
    
    @staticmethod
    def create_transformation_request(
        profiles: List[LinkedInProfile],
        options: Optional[Dict[str, Any]] = None
    ) -> SchemaTransformationRequest:
        """Create a standardized schema transformation request."""
        default_options = {
            "use_llm_enhancement": True,
            "validate_output": True,
            "include_confidence_scores": True
        }
        
        if options:
            default_options.update(options)
        
        return SchemaTransformationRequest(
            linkedin_profiles=profiles,
            target_schema="smarthr_candidate",
            transformation_options=default_options
        )
    
    @staticmethod
    def validate_communication_data(data: Dict[str, Any], expected_type: str) -> bool:
        """Validate data structure for agent communication."""
        try:
            if expected_type == "search_request":
                required_fields = ["filters", "limit"]
                return all(field in data for field in required_fields)
            
            elif expected_type == "search_response":
                required_fields = ["profiles", "success"]
                return all(field in data for field in required_fields)
            
            elif expected_type == "transformation_request":
                required_fields = ["linkedin_profiles", "target_schema"]
                return all(field in data for field in required_fields)
            
            elif expected_type == "transformation_response":
                required_fields = ["transformed_candidates", "success"]
                return all(field in data for field in required_fields)
            
            return False
            
        except Exception:
            return False


# Convenience functions
async def search_linkedin_candidates(
    config: LinkedInIntegrationConfig,
    keywords: List[str],
    **search_params
) -> Tuple[LinkedInSearchResponse, Optional[SchemaTransformationResponse]]:
    """Convenience function for LinkedIn candidate search and transformation."""
    orchestrator = LinkedInAgentOrchestrator(config)
    
    search_request = LinkedInAgentCommunicationProtocol.create_search_request(
        keywords=keywords,
        **search_params
    )
    
    return await orchestrator.search_and_transform_candidates(search_request)


def create_linkedin_orchestrator(config: LinkedInIntegrationConfig) -> LinkedInAgentOrchestrator:
    """Create LinkedIn agent orchestrator with configuration."""
    return LinkedInAgentOrchestrator(config)
