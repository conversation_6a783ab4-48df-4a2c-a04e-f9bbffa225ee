import logging
import time
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field, model_validator

from models.linkedin import (
    LinkedInSearchRequest as CoreLinkedInSearchRequest,
    LinkedInSearchFilters,
    SchemaTransformationRequest,
    LinkedInProfile
)
from models.linkedin_config import LinkedInIntegrationConfig, load_linkedin_config, get_active_linkedin_config
from models.comprehensive_candidate import (
    ComprehensiveCandidateResult, ExtractionCost, BatchCandidateResults
)
from controllers.linkedin_workflow_orchestrator import (
    LinkedInWorkflowOrchestrator,
    execute_linkedin_candidate_search
)
from controllers.linkedin_agents import LinkedInSearchAgent, SchemaTransformationAgent
from utils.linkedin_agent_communication import LinkedInAgentCommunicationProtocol
from utils.linkedin_schema_mapper import map_linkedin_to_comprehensive_format

logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter(prefix="/external_source", tags=["LinkedIn Integration"])

# Load configuration
try:
    external_source_config = load_linkedin_config()
    logger.info("External source configuration loaded successfully")
except Exception as e:
    logger.error(f"Failed to load external source configuration: {str(e)}")
    external_source_config = None


# Unified Request/Response Models for Simplified API
class LinkedInSearchRequest(BaseModel):
    """Unified request model for all LinkedIn search operations."""
    # Core search parameters
    keywords: List[str] = Field(..., description="Keywords to search for people")
    location: Optional[str] = Field(None, description="Location filter")
    experience_level: Optional[str] = Field(None, description="Experience level filter")
    skills: Optional[List[str]] = Field(None, description="Skills filter")
    school: Optional[str] = Field(None, description="School/university filter")

    # Search behavior
    limit: int = Field(default=25, ge=1, le=100, description="Maximum number of results per search")
    batch_searches: Optional[List[Dict[str, Any]]] = Field(None, description="Multiple search requests for batch processing")

    # Response options
    transform_profiles: bool = Field(default=False, description="Whether to transform profiles to smartHR format")
    include_raw_profiles: bool = Field(default=True, description="Whether to include raw LinkedIn profiles in response")

    # Advanced options
    test_mode: bool = Field(default=False, description="Run in test mode with limited results")
    max_concurrent: int = Field(default=3, ge=1, le=10, description="Maximum concurrent searches for batch operations")


class LinkedInTransformRequest(BaseModel):
    """Unified request model for all LinkedIn transformation operations."""
    # Profile sources - either direct profiles or batch results containing profiles
    profiles: Optional[Any] = Field(None, description="LinkedIn profiles to transform to standard format")
    batch_results: Optional[List[Dict[str, Any]]] = Field(None, description="Batch operation results containing profiles")

    # Transformation options
    include_quality_assessment: bool = Field(default=True, description="Whether to include quality assessment")
    use_llm_enhancement: bool = Field(default=True, description="Whether to use LLM for enhanced transformation")
    validate_output: bool = Field(default=True, description="Whether to validate transformed output")
    include_confidence_scores: bool = Field(default=True, description="Whether to include transformation confidence scores")

    # Batch processing
    batch_size: int = Field(default=10, ge=1, le=50, description="Batch size for processing multiple profiles")
    max_concurrent: int = Field(default=5, ge=1, le=20, description="Maximum concurrent transformations")




class LinkedInSearchResponse(BaseModel):
    """Unified response model for LinkedIn search operations."""
    success: bool
    total_results: int
    profiles_returned: int
    profiles: Optional[List[Dict[str, Any]]] = None
    transformed_candidates: Optional[List[Dict[str, Any]]] = None

    # Search metadata
    search_id: str
    search_filters_used: Dict[str, Any]
    execution_time_ms: Optional[int] = None

    # Batch results (if applicable)
    batch_results: Optional[List[Dict[str, Any]]] = None
    batch_summary: Optional[Dict[str, Any]] = None

    # Error handling
    error_message: Optional[str] = None
    warnings: List[str] = []
    timestamp: str


class LinkedInTransformResponse(BaseModel):
    """Unified response model for LinkedIn transformation operations."""
    success: bool
    profiles_input: int
    profiles_transformed: int
    transformed_profiles: List[Dict[str, Any]]

    # Quality metrics
    transformation_summary: Optional[Dict[str, Any]] = None
    average_confidence: Optional[float] = None
    failed_transformations: List[Dict[str, Any]] = []

    # Cost tracking
    total_extraction_cost: Optional[Dict[str, Any]] = None
    processing_time_ms: Optional[int] = None

    # Error handling
    error_message: Optional[str] = None
    warnings: List[str] = []
    timestamp: str


class LinkedInHealthResponse(BaseModel):
    """Response model for LinkedIn integration health check."""
    status: str = Field(..., description="Overall health status: healthy, degraded, unhealthy")
    timestamp: str = Field(..., description="Health check timestamp")
    system_info: Dict[str, Any] = Field(..., description="System information")
    configuration_status: Dict[str, Any] = Field(..., description="Configuration validation status")
    agents_status: Dict[str, Any] = Field(..., description="LinkedIn agents health status")
    dependencies: Dict[str, Any] = Field(..., description="External dependencies status")
    metrics: Dict[str, Any] = Field(..., description="System metrics and statistics")
    issues: List[str] = Field(default=[], description="List of identified issues")
    recommendations: List[str] = Field(default=[], description="Recommendations for improvements")


class LinkedInConfigResponse(BaseModel):
    """Response model for LinkedIn integration configuration (sanitized)."""
    provider: str = Field(..., description="LinkedIn API provider")
    api_version: str = Field(..., description="API version")
    rate_limits: Dict[str, Any] = Field(..., description="Rate limiting configuration")
    search_limits: Dict[str, Any] = Field(..., description="Search operation limits")
    transformation_config: Dict[str, Any] = Field(..., description="Schema transformation settings")
    agent_config: Dict[str, Any] = Field(..., description="Agent configuration settings")
    system_settings: Dict[str, Any] = Field(..., description="System-wide settings")
    features_enabled: Dict[str, bool] = Field(..., description="Feature flags and enabled capabilities")
    last_updated: str = Field(..., description="Configuration last updated timestamp")


# Health Check Helper Functions
def perform_comprehensive_health_check() -> LinkedInHealthResponse:
    """Perform comprehensive health check of LinkedIn integration."""
    start_time = time.time()
    issues = []
    recommendations = []

    try:
        # System info
        system_info = {
            "service": "LinkedIn Integration",
            "version": "1.0.0",
            "environment": "production",
            "uptime_seconds": int(time.time() - start_time)
        }

        # Configuration validation
        config_status = check_configuration_health()
        if not config_status["valid"]:
            issues.extend(config_status["issues"])
            recommendations.extend(config_status["recommendations"])

        # Agents health check
        agents_status = check_agents_health()
        if not agents_status["all_healthy"]:
            issues.extend(agents_status["issues"])
            recommendations.extend(agents_status["recommendations"])

        # Dependencies check
        dependencies = check_dependencies_health()
        if not dependencies["all_available"]:
            issues.extend(dependencies["issues"])
            recommendations.extend(dependencies["recommendations"])

        # System metrics
        metrics = get_system_metrics()

        # Determine overall status
        if len(issues) == 0:
            overall_status = "healthy"
        elif any("critical" in issue.lower() or "error" in issue.lower() for issue in issues):
            overall_status = "unhealthy"
        else:
            overall_status = "degraded"

        return LinkedInHealthResponse(
            status=overall_status,
            timestamp=datetime.now().isoformat(),
            system_info=system_info,
            configuration_status=config_status,
            agents_status=agents_status,
            dependencies=dependencies,
            metrics=metrics,
            issues=issues,
            recommendations=recommendations
        )

    except Exception as e:
        logger.error(f"Health check failed with exception: {str(e)}")
        return LinkedInHealthResponse(
            status="unhealthy",
            timestamp=datetime.now().isoformat(),
            system_info={"service": "LinkedIn Integration", "error": "Health check failed"},
            configuration_status={"valid": False, "error": str(e)},
            agents_status={"all_healthy": False, "error": str(e)},
            dependencies={"all_available": False, "error": str(e)},
            metrics={},
            issues=[f"Health check system error: {str(e)}"],
            recommendations=["Check system logs for detailed error information"]
        )


def check_configuration_health() -> Dict[str, Any]:
    """Check LinkedIn configuration health."""
    try:
        if external_source_config is None:
            return {
                "valid": False,
                "status": "configuration_not_loaded",
                "issues": ["LinkedIn configuration failed to load"],
                "recommendations": ["Check environment variables and configuration files"]
            }

        # Validate configuration
        config_issues = external_source_config.validate_config()

        # Safely get provider value (handle both enum and string cases)
        try:
            provider_value = external_source_config.api_config.provider.value if hasattr(external_source_config.api_config.provider, 'value') else str(external_source_config.api_config.provider)
        except:
            provider_value = "unknown"

        return {
            "valid": len(config_issues) == 0,
            "status": "valid" if len(config_issues) == 0 else "invalid",
            "provider": provider_value,
            "api_version": external_source_config.api_config.api_version,
            "issues": config_issues,
            "recommendations": [
                "Verify LinkedIn API credentials",
                "Check rate limiting configuration",
                "Ensure all required environment variables are set"
            ] if config_issues else []
        }

    except Exception as e:
        return {
            "valid": False,
            "status": "error",
            "error": str(e),
            "issues": [f"Configuration validation error: {str(e)}"],
            "recommendations": ["Check configuration system and environment setup"]
        }


def check_agents_health() -> Dict[str, Any]:
    """Check LinkedIn agents health."""
    try:
        if external_source_config is None:
            return {
                "all_healthy": False,
                "agents": {},
                "issues": ["Cannot check agents - configuration not available"],
                "recommendations": ["Fix configuration issues first"]
            }

        agents_status = {}
        all_healthy = True
        issues = []
        recommendations = []

        # Check Search Agent
        try:
            search_agent = LinkedInSearchAgent(external_source_config)
            search_health = search_agent.health_check()
            agents_status["search_agent"] = search_health

            if search_health["status"] != "healthy":
                all_healthy = False
                issues.append(f"Search agent unhealthy: {search_health.get('error', 'Unknown issue')}")
                recommendations.append("Check search agent configuration and dependencies")

        except Exception as e:
            all_healthy = False
            agents_status["search_agent"] = {"status": "error", "error": str(e)}
            issues.append(f"Search agent error: {str(e)}")
            recommendations.append("Check search agent initialization")

        # Check Transformation Agent
        try:
            transform_agent = SchemaTransformationAgent(external_source_config)
            transform_health = transform_agent.health_check()
            agents_status["transformation_agent"] = transform_health

            if transform_health["status"] != "healthy":
                all_healthy = False
                issues.append(f"Transformation agent unhealthy: {transform_health.get('error', 'Unknown issue')}")
                recommendations.append("Check transformation agent configuration and LLM connectivity")

        except Exception as e:
            all_healthy = False
            agents_status["transformation_agent"] = {"status": "error", "error": str(e)}
            issues.append(f"Transformation agent error: {str(e)}")
            recommendations.append("Check transformation agent initialization and LLM setup")

        return {
            "all_healthy": all_healthy,
            "agents": agents_status,
            "issues": issues,
            "recommendations": recommendations
        }

    except Exception as e:
        return {
            "all_healthy": False,
            "agents": {},
            "error": str(e),
            "issues": [f"Agents health check error: {str(e)}"],
            "recommendations": ["Check agents system and configuration"]
        }


def check_dependencies_health() -> Dict[str, Any]:
    """Check external dependencies health."""
    dependencies = {}
    all_available = True
    issues = []
    recommendations = []

    # Check if we can import required modules
    try:
        import httpx
        dependencies["httpx"] = {"status": "available", "version": getattr(httpx, "__version__", "unknown")}
    except ImportError:
        all_available = False
        dependencies["httpx"] = {"status": "unavailable"}
        issues.append("HTTP client library (httpx) not available")
        recommendations.append("Install httpx library")

    # Check LLM connectivity (for transformation agent)
    try:
        from models.llm import inference_with_fallback
        dependencies["llm_system"] = {"status": "available"}
    except ImportError:
        dependencies["llm_system"] = {"status": "unavailable"}
        issues.append("LLM system not available")
        recommendations.append("Check LLM configuration and connectivity")

    # Check configuration loading
    try:
        from models.linkedin_config import load_linkedin_config
        dependencies["config_system"] = {"status": "available"}
    except ImportError:
        all_available = False
        dependencies["config_system"] = {"status": "unavailable"}
        issues.append("Configuration system not available")
        recommendations.append("Check configuration module")

    return {
        "all_available": all_available,
        "dependencies": dependencies,
        "issues": issues,
        "recommendations": recommendations
    }


def get_system_metrics() -> Dict[str, Any]:
    """Get system metrics and statistics."""
    try:
        return {
            "memory_usage": "N/A",  # Could implement actual memory monitoring
            "active_connections": 0,  # Could track active API connections
            "requests_processed": 0,  # Could implement request counting
            "cache_hit_rate": 0.0,  # Could implement cache monitoring
            "average_response_time_ms": 0,  # Could implement response time tracking
            "last_successful_request": "N/A",  # Could track last successful operation
            "error_rate_24h": 0.0,  # Could implement error rate tracking
            "uptime_hours": 0.0  # Could implement uptime tracking
        }
    except Exception as e:
        logger.warning(f"Failed to collect system metrics: {str(e)}")
        return {"error": "Metrics collection failed"}


def get_sanitized_config() -> LinkedInConfigResponse:
    """Get sanitized configuration information (no sensitive data)."""
    try:
        if external_source_config is None:
            raise ValueError("Configuration not loaded")

        config = external_source_config

        # Safely get provider value (handle both enum and string cases)
        try:
            provider_value = config.api_config.provider.value if hasattr(config.api_config.provider, 'value') else str(config.api_config.provider)
        except:
            provider_value = "unknown"

        return LinkedInConfigResponse(
            provider=provider_value,
            api_version=config.api_config.api_version,
            rate_limits={
                "requests_per_minute": config.api_config.rate_limits.requests_per_minute,
                "requests_per_hour": config.api_config.rate_limits.requests_per_hour,
                "requests_per_day": config.api_config.rate_limits.requests_per_day,
                "timeout_seconds": config.api_config.rate_limits.timeout_seconds,
                "max_retries": config.api_config.rate_limits.max_retries
            },
            search_limits={
                "default_limit": config.api_config.default_search_limit,
                "max_limit": config.api_config.max_search_limit
            },
            transformation_config={
                "llm_models_order": config.transformation_config.llm_models_order,
                "transformation_temperature": config.transformation_config.transformation_temperature,
                "max_transformation_retries": config.transformation_config.max_transformation_retries,
                "validation_enabled": config.transformation_config.validation_enabled,
                "strict_validation": config.transformation_config.strict_validation,
                "use_fallback_transformation": config.transformation_config.use_fallback_transformation,
                "confidence_threshold": config.transformation_config.confidence_threshold,
                "enable_data_enrichment": config.transformation_config.enable_data_enrichment,
                "minimum_confidence_score": config.transformation_config.minimum_confidence_score
            },
            agent_config={
                "timeout_seconds": config.agent_config.agent_timeout_seconds,
                "enable_parallel_processing": config.agent_config.enable_parallel_processing,
                "max_concurrent_requests": config.agent_config.max_concurrent_requests
            },
            system_settings={
                "enable_monitoring": config.enable_monitoring,
                "enable_metrics": config.enable_metrics,
                "enable_health_checks": config.enable_health_checks,
                "health_check_interval_seconds": config.health_check_interval_seconds,
                "enable_error_recovery": config.enable_error_recovery,
                "max_system_retries": config.max_system_retries
            },
            features_enabled={
                "caching": config.api_config.enable_caching,
                "logging": config.api_config.enable_logging,
                "profile_enrichment": config.api_config.enable_profile_enrichment,
                "data_enrichment": config.transformation_config.enable_data_enrichment,
                "parallel_processing": config.agent_config.enable_parallel_processing,
                "fallback_transformation": config.transformation_config.use_fallback_transformation,
                "validation": config.transformation_config.validation_enabled
            },
            last_updated=datetime.now().isoformat()
        )

    except Exception as e:
        logger.error(f"Failed to get sanitized config: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve configuration: {str(e)}"
        )


# Utility Functions
def extract_profiles_from_request(request: LinkedInTransformRequest) -> List[Dict[str, Any]]:
    """Extract profiles from either direct profiles field or batch_results."""
    profiles = []

    # First, try to get profiles from the direct profiles field
    if request.profiles:
        if not isinstance(request.profiles, list):
            raise HTTPException(
                status_code=400,
                detail="'profiles' field must be a list of profile objects"
            )
        profiles.extend(request.profiles)

    # Then, try to extract profiles from batch_results
    if request.batch_results:
        if not isinstance(request.batch_results, list):
            raise HTTPException(
                status_code=400,
                detail="'batch_results' field must be a list of batch result objects"
            )

        for i, batch_result in enumerate(request.batch_results):
            # Check if this batch result has profiles
            if isinstance(batch_result, dict) and "profiles" in batch_result:
                batch_profiles = batch_result["profiles"]
                if isinstance(batch_profiles, list):
                    profiles.extend(batch_profiles)
                elif batch_profiles is not None:
                    raise HTTPException(
                        status_code=400,
                        detail=f"'profiles' in batch_results[{i}] must be a list or null"
                    )

    # Validate that we have at least one source of profiles
    if not request.profiles and not request.batch_results:
        raise HTTPException(
            status_code=400,
            detail="Either 'profiles' or 'batch_results' must be provided"
        )

    return profiles


# Validation Functions
def validate_search_request(request: LinkedInSearchRequest) -> None:
    """Validate LinkedIn search request parameters."""
    if not request.keywords:
        raise HTTPException(
            status_code=400,
            detail="At least one keyword is required for search"
        )

    if len(request.keywords) > 10:
        raise HTTPException(
            status_code=400,
            detail="Maximum 10 keywords allowed per search"
        )

    if request.batch_searches:
        if len(request.batch_searches) > 20:
            raise HTTPException(
                status_code=400,
                detail="Maximum 20 batch searches allowed per request"
            )

        for i, batch in enumerate(request.batch_searches):
            if not batch.get('keywords'):
                raise HTTPException(
                    status_code=400,
                    detail=f"Batch search {i+1} missing required 'keywords' field. Each batch search must include a 'keywords' array. Example: {{\"keywords\": [\"engineer\"], \"location\": \"Colombia\"}}"
                )


def validate_transform_request(request: LinkedInTransformRequest) -> None:
    """Validate LinkedIn transformation request parameters."""
    # Extract profiles from either direct profiles or batch results
    profiles = extract_profiles_from_request(request)

    if not profiles:
        raise HTTPException(
            status_code=400,
            detail="At least one profile is required for transformation. Provide either 'profiles' or 'batch_results' containing profiles."
        )

    if len(profiles) > 100:
        raise HTTPException(
            status_code=400,
            detail="Maximum 100 profiles allowed per transformation request"
        )

    # Validate each profile has minimum required fields
    for i, profile in enumerate(profiles):
        if not isinstance(profile, dict):
            raise HTTPException(
                status_code=400,
                detail=f"Profile {i+1} must be a valid object"
            )

        required_fields = ['first_name', 'last_name']
        missing_fields = [field for field in required_fields if not profile.get(field)]

        if missing_fields:
            raise HTTPException(
                status_code=400,
                detail=f"Profile {i+1} missing required fields: {', '.join(missing_fields)}"
            )





@router.post("/linkedin/search", response_model=LinkedInSearchResponse)
async def linkedin_search(request: LinkedInSearchRequest):
    """
    Unified LinkedIn search endpoint that handles all search operations.

    This endpoint consolidates the functionality of:
    - Regular search with optional transformation
    - Search-only operations (no transformation)
    - Batch search operations
    - Test mode searches

    Args:
        request: Unified LinkedIn search request with all options

    Returns:
        Comprehensive search results with optional transformation data
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="LinkedIn integration not configured"
            )

        # Validate request
        validate_search_request(request)

        start_time = time.time()

        # Handle test mode
        if request.test_mode:
            request.limit = min(request.limit, 5)  # Limit test results
            logger.info(f"LinkedIn search in TEST MODE: {len(request.keywords)} keywords, limit: {request.limit}")
        else:
            logger.info(f"LinkedIn search request: {len(request.keywords)} keywords, limit: {request.limit}")

        # Handle batch searches
        if request.batch_searches:
            return await _handle_batch_search(request, external_source_config)

        # Handle single search
        return await _handle_single_search(request, external_source_config, start_time)

    except Exception as e:
        logger.error(f"LinkedIn search failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"LinkedIn search failed: {str(e)}"
        )


async def _handle_single_search(request: LinkedInSearchRequest, config: LinkedInIntegrationConfig, start_time: float) -> LinkedInSearchResponse:
    """Handle single LinkedIn search operation."""
    from models.linkedin import LinkedInSearchFilters, LinkedInSearchRequest as CoreSearchRequest

    # Create search request
    search_request = CoreSearchRequest(
        filters=LinkedInSearchFilters(
            keywords=request.keywords,
            location=request.location,
            experience_level=request.experience_level,
            skills=request.skills,
            school=request.school,
            limit=request.limit
        )
    )

    # Execute search
    if request.transform_profiles:
        # Full workflow with transformation
        workflow_result = await execute_linkedin_candidate_search(
            config=config,
            keywords=request.keywords,
            location=request.location,
            experience_level=request.experience_level,
            skills=request.skills,
            school=request.school,
            limit=request.limit
        )

        return LinkedInSearchResponse(
            success=True,
            total_results=workflow_result['workflow_summary']['profiles_found'],
            profiles_returned=workflow_result['workflow_summary']['profiles_found'],
            profiles=[profile.model_dump() for profile in workflow_result['search_results']['profiles']] if request.include_raw_profiles else None,
            transformed_candidates=workflow_result.get('transformation_results', {}).get('transformed_candidates'),
            search_id=workflow_result['search_results']['search_id'],
            search_filters_used=workflow_result['search_results']['search_filters_used'],
            execution_time_ms=int((time.time() - start_time) * 1000),
            timestamp=datetime.now().isoformat()
        )
    else:
        # Search-only operation
        orchestrator = LinkedInWorkflowOrchestrator(config)
        search_result = await orchestrator.execute_search_step(search_request)

        return LinkedInSearchResponse(
            success=True,
            total_results=search_result.total_results,
            profiles_returned=search_result.returned_results,
            profiles=[profile.model_dump() for profile in search_result.profiles] if request.include_raw_profiles else None,
            search_id=search_result.search_id,
            search_filters_used=search_result.search_filters_used.model_dump(),
            execution_time_ms=int((time.time() - start_time) * 1000),
            timestamp=datetime.now().isoformat()
        )


async def _handle_batch_search(request: LinkedInSearchRequest, config: LinkedInIntegrationConfig) -> LinkedInSearchResponse:
    """Handle batch LinkedIn search operations."""
    batch_results = []
    total_profiles = 0
    total_transformed = 0

    # Process batch searches with concurrency control
    semaphore = asyncio.Semaphore(request.max_concurrent)

    async def process_single_batch(batch_request_data: Dict[str, Any]) -> Dict[str, Any]:
        async with semaphore:
            # Create individual search request
            batch_search_request = LinkedInSearchRequest(
                keywords=batch_request_data.get('keywords', request.keywords),
                location=batch_request_data.get('location', request.location),
                experience_level=batch_request_data.get('experience_level', request.experience_level),
                skills=batch_request_data.get('skills', request.skills),
                school=batch_request_data.get('school', request.school),
                limit=batch_request_data.get('limit', request.limit),
                transform_profiles=request.transform_profiles,
                include_raw_profiles=request.include_raw_profiles
            )

            # Execute single search
            result = await _handle_single_search(batch_search_request, config, time.time())
            return result.model_dump()

    # Execute all batch searches
    tasks = [process_single_batch(batch_data) for batch_data in request.batch_searches]
    batch_results = await asyncio.gather(*tasks, return_exceptions=True)

    # Process results
    successful_results = []
    failed_results = []

    for i, result in enumerate(batch_results):
        if isinstance(result, Exception):
            failed_results.append({
                "batch_index": i,
                "error": str(result)
            })
        else:
            successful_results.append(result)
            total_profiles += result.get('total_results', 0)
            if result.get('transformed_candidates'):
                total_transformed += len(result.get('transformed_candidates', []))

    return LinkedInSearchResponse(
        success=len(successful_results) > 0,
        total_results=total_profiles,
        profiles_returned=total_profiles,
        batch_results=successful_results,
        batch_summary={
            "total_batches": len(request.batch_searches),
            "successful_batches": len(successful_results),
            "failed_batches": len(failed_results),
            "total_profiles_found": total_profiles,
            "total_profiles_transformed": total_transformed,
            "failed_batch_details": failed_results
        },
        search_id=f"batch_{int(time.time())}",
        search_filters_used={"batch_operation": True},
        execution_time_ms=None,  # Will be calculated by the main endpoint
        timestamp=datetime.now().isoformat(),
        warnings=[f"Failed batches: {len(failed_results)}"] if failed_results else []
    )


@router.post("/linkedin/transform/debug")
async def linkedin_transform_debug(request: LinkedInTransformRequest):
    """Debug endpoint to test the model validation."""
    return {
        "message": "Model validation successful!",
        "profiles": request.profiles,
        "batch_results": request.batch_results,
        "profiles_type": type(request.profiles).__name__,
        "batch_results_type": type(request.batch_results).__name__
    }


@router.post("/linkedin/transform", response_model=LinkedInTransformResponse)
async def linkedin_transform(request: LinkedInTransformRequest):
    """
    Unified LinkedIn transformation endpoint that handles all transformation operations.

    This endpoint consolidates the functionality of:
    - Single profile transformation
    - Batch profile transformation
    - Quality assessment and confidence scoring
    - LLM-enhanced transformation

    Args:
        request: Unified LinkedIn transformation request with all options

    Returns:
        Comprehensive transformation results with quality metrics
    """
    try:
        if not external_source_config:
            raise HTTPException(
                status_code=503,
                detail="LinkedIn integration not configured"
            )

        # Validate request
        validate_transform_request(request)

        # Extract profiles from request (handles both direct profiles and batch_results)
        profiles = extract_profiles_from_request(request)

        start_time = time.time()
        logger.info(f"LinkedIn transform request: {len(profiles)} profiles to transform")

        # Handle batch transformation with concurrency control
        return await _handle_batch_transformation(request, external_source_config, start_time)

    except Exception as e:
        logger.error(f"LinkedIn transformation failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"LinkedIn transformation failed: {str(e)}"
        )


async def _handle_batch_transformation(request: LinkedInTransformRequest, config: LinkedInIntegrationConfig, start_time: float) -> LinkedInTransformResponse:
    """Handle batch LinkedIn transformation operations."""
    from models.linkedin import LinkedInProfile

    # Extract profiles from request (handles both direct profiles and batch_results)
    profiles = extract_profiles_from_request(request)

    transformed_responses = []
    failed_transformations = []
    total_tokens_used = 0
    confidence_scores = []

    # Process profiles in batches with concurrency control
    semaphore = asyncio.Semaphore(request.max_concurrent)

    async def process_single_profile(profile_data: Dict[str, Any], index: int) -> Dict[str, Any]:
        async with semaphore:
            try:
                # Convert dict to LinkedInProfile object for validation
                linkedin_profile = LinkedInProfile(**profile_data)

                if request.use_llm_enhancement:
                    # Use the existing transformation agent for LLM-enhanced transformation
                    from controllers.linkedin_agents import SchemaTransformationAgent
                    from models.linkedin import SchemaTransformationRequest as CoreTransformRequest

                    agent = SchemaTransformationAgent(config)
                    transform_request = CoreTransformRequest(
                        linkedin_profiles=[linkedin_profile],
                        target_schema="smarthr_candidate"
                    )

                    result = agent.process_request(transform_request)

                    if result.success and result.transformed_candidates:
                        candidate = result.transformed_candidates[0]
                        confidence_scores.append(candidate.transformation_confidence)

                        # Convert old format to comprehensive format if needed
                        if 'personal_info' not in candidate.candidate_info:
                            # Convert from old format to comprehensive format using original LinkedIn data
                            comprehensive_data = transform_linkedin_to_standard_format(profile_data)
                        else:
                            # Already in comprehensive format
                            comprehensive_data = candidate.candidate_info

                        # Mock extraction cost for now
                        extraction_cost = ExtractionCost(
                            prompt_tokens=1200,
                            completion_tokens=600,
                            total_tokens=1800,
                            cost=0.009
                        )

                        return ComprehensiveCandidateResult(
                            response=comprehensive_data,
                            extraction_cost=extraction_cost
                        ).model_dump()
                    else:
                        raise Exception(f"LLM transformation failed: {result.error_message}")
                else:
                    # Use simple rule-based transformation
                    transformed_data = transform_linkedin_to_standard_format(profile_data)

                    # Mock extraction cost for rule-based transformation
                    extraction_cost = ExtractionCost(
                        prompt_tokens=0,
                        completion_tokens=0,
                        total_tokens=0,
                        cost=0.0
                    )

                    return ComprehensiveCandidateResult(
                        response=transformed_data,
                        extraction_cost=extraction_cost
                    ).model_dump()

            except Exception as e:
                return {
                    "error": str(e),
                    "profile_index": index,
                    "profile_id": profile_data.get("id", "unknown")
                }

    # Process all profiles
    tasks = [process_single_profile(profile, i) for i, profile in enumerate(profiles)]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Separate successful and failed transformations
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            failed_transformations.append({
                "profile_index": i,
                "error": str(result),
                "profile_id": profiles[i].get("id", "unknown")
            })
        elif "error" in result:
            failed_transformations.append(result)
        else:
            transformed_responses.append(result)
            total_tokens_used += result["extraction_cost"]["total_tokens"]
            if result.get("confidence_score"):
                confidence_scores.append(result["confidence_score"])

    # Calculate metrics
    average_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
    total_cost = sum(r["extraction_cost"]["cost"] for r in transformed_responses)

    return LinkedInTransformResponse(
        success=len(transformed_responses) > 0,
        profiles_input=len(profiles),
        profiles_transformed=len(transformed_responses),
        transformed_profiles=transformed_responses,
        transformation_summary={
            "successful_transformations": len(transformed_responses),
            "failed_transformations": len(failed_transformations),
            "average_confidence": average_confidence,
            "llm_enhanced": request.use_llm_enhancement,
            "validation_enabled": request.validate_output
        },
        average_confidence=average_confidence,
        failed_transformations=failed_transformations,
        total_extraction_cost={
            "total_tokens": total_tokens_used,
            "total_cost": total_cost
        },
        processing_time_ms=int((time.time() - start_time) * 1000),
        timestamp=datetime.now().isoformat(),
        warnings=[f"Failed transformations: {len(failed_transformations)}"] if failed_transformations else []
    )


@router.get("/health", response_model=LinkedInHealthResponse)
async def linkedin_health_check():
    """
    LinkedIn integration health check endpoint.

    Provides comprehensive health status including:
    - Overall system health
    - Configuration validation
    - Agent status
    - Dependencies check
    - System metrics
    - Issues and recommendations

    Returns:
        Comprehensive health status with recommendations
    """
    try:
        logger.info("Performing LinkedIn integration health check")
        health_response = perform_comprehensive_health_check()

        # Log health status
        if health_response.status == "healthy":
            logger.info("LinkedIn integration health check: HEALTHY")
        elif health_response.status == "degraded":
            logger.warning(f"LinkedIn integration health check: DEGRADED - Issues: {health_response.issues}")
        else:
            logger.error(f"LinkedIn integration health check: UNHEALTHY - Issues: {health_response.issues}")

        return health_response

    except Exception as e:
        logger.error(f"Health check endpoint failed: {str(e)}")
        return LinkedInHealthResponse(
            status="unhealthy",
            timestamp=datetime.now().isoformat(),
            system_info={"service": "LinkedIn Integration", "error": "Health check endpoint failed"},
            configuration_status={"valid": False, "error": str(e)},
            agents_status={"all_healthy": False, "error": str(e)},
            dependencies={"all_available": False, "error": str(e)},
            metrics={},
            issues=[f"Health check endpoint error: {str(e)}"],
            recommendations=["Check system logs and restart service if necessary"]
        )


@router.get("/config", response_model=LinkedInConfigResponse)
async def linkedin_get_config():
    """
    Get LinkedIn integration configuration information.

    Returns sanitized configuration without sensitive data like:
    - API keys, passwords, or tokens
    - Internal system details
    - Security-sensitive settings

    Includes:
    - Provider and API version
    - Rate limiting settings
    - Feature flags
    - System settings
    - Agent configuration

    Returns:
        Sanitized configuration information
    """
    try:
        logger.info("Retrieving LinkedIn integration configuration")

        if external_source_config is None:
            raise HTTPException(
                status_code=503,
                detail="LinkedIn integration not configured"
            )

        config_response = get_sanitized_config()
        logger.info("LinkedIn configuration retrieved successfully")
        return config_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Config endpoint failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve configuration: {str(e)}"
        )


# Utility Functions
def transform_linkedin_to_standard_format(linkedin_profile: dict) -> dict:
    """
    Transform LinkedIn profile to the standard candidate format shown in interactive feedback.

    Args:
        linkedin_profile: LinkedIn profile data as dict

    Returns:
        Transformed candidate data in the expected format
    """
    from datetime import datetime

    # Helper function to safely get nested values
    def safe_get(data, *keys, default=None):
        for key in keys:
            if isinstance(data, dict) and key in data:
                data = data[key]
            else:
                return default
        return data if data is not None else default

    # Extract location information
    location_data = safe_get(linkedin_profile, 'location', default={})
    city = safe_get(location_data, 'name', default=None)
    country = safe_get(location_data, 'country', default=None)

    # Build personal_info section
    personal_info = {
        "full_name": safe_get(linkedin_profile, 'full_name') or f"{safe_get(linkedin_profile, 'first_name', default='')} {safe_get(linkedin_profile, 'last_name', default='')}".strip() or None,
        "country": country,
        "city": city,
        "address": None,  # LinkedIn doesn't provide detailed addresses
        "phone_number": None,  # LinkedIn doesn't provide phone numbers
        "email": None,  # LinkedIn doesn't provide email addresses
        "linkedin_profile": safe_get(linkedin_profile, 'profile_url'),
        "website": None  # Could be extracted from additional info if available
    }

    # Build education section
    education = []
    linkedin_education = safe_get(linkedin_profile, 'education', default=[])
    for edu in linkedin_education:
        education_entry = {
            "institution_name": safe_get(edu, 'school'),
            "degree": safe_get(edu, 'degree'),
            "field_of_study": safe_get(edu, 'field_of_study'),
            "start_date": str(safe_get(edu, 'start_year')) if safe_get(edu, 'start_year') else None,
            "end_date": str(safe_get(edu, 'end_year')) if safe_get(edu, 'end_year') else None,
            "location": None,  # LinkedIn education doesn't always have location
            "description": safe_get(edu, 'description')
        }
        education.append(education_entry)

    # Build work_experience section
    work_experience = []
    linkedin_experience = safe_get(linkedin_profile, 'experience', default=[])
    for exp in linkedin_experience:
        # Extract company name
        company_data = safe_get(exp, 'company', default={})
        company_name = safe_get(company_data, 'name') if isinstance(company_data, dict) else str(company_data) if company_data else None

        # Format dates
        start_date = safe_get(exp, 'start_date')
        end_date = safe_get(exp, 'end_date')
        if safe_get(exp, 'is_current'):
            end_date = "Present"

        # Extract location
        exp_location_data = safe_get(exp, 'location', default={})
        exp_location = safe_get(exp_location_data, 'name') if isinstance(exp_location_data, dict) else str(exp_location_data) if exp_location_data else None

        # Build responsibilities list
        responsibilities = []
        description = safe_get(exp, 'description')
        if description:
            # Split description into bullet points if it contains multiple sentences
            if '.' in description and len(description) > 100:
                sentences = [s.strip() for s in description.split('.') if s.strip()]
                responsibilities = [s + '.' for s in sentences if len(s) > 10]
            else:
                responsibilities = [description]

        work_entry = {
            "job_title": safe_get(exp, 'title'),
            "company_name": company_name,
            "start_date": start_date,
            "end_date": end_date,
            "location": exp_location,
            "responsibilities": responsibilities,
            "skills": None  # LinkedIn experience doesn't typically have separate skills
        }
        work_experience.append(work_entry)

    # Build skills section
    skills = []
    linkedin_skills = safe_get(linkedin_profile, 'skills', default=[])
    for skill in linkedin_skills:
        skill_name = safe_get(skill, 'name') if isinstance(skill, dict) else str(skill) if skill else None
        if skill_name:
            skill_entry = {
                "name": skill_name,
                "proficiency_level": None,  # LinkedIn doesn't provide proficiency levels
                "years_of_experience": None  # LinkedIn doesn't provide years of experience
            }
            skills.append(skill_entry)

    # Build languages section
    languages = []
    linkedin_languages = safe_get(linkedin_profile, 'languages', default=[])
    for lang in linkedin_languages:
        if isinstance(lang, str):
            language_entry = {
                "language": lang,
                "proficiency_level": None  # LinkedIn doesn't always provide proficiency levels
            }
            languages.append(language_entry)
        elif isinstance(lang, dict):
            language_entry = {
                "language": safe_get(lang, 'name') or safe_get(lang, 'language'),
                "proficiency_level": safe_get(lang, 'proficiency_level')
            }
            languages.append(language_entry)

    # Build certifications section
    certifications = []
    linkedin_certifications = safe_get(linkedin_profile, 'certifications', default=[])
    for cert in linkedin_certifications:
        if isinstance(cert, str):
            cert_entry = {
                "name": cert,
                "issuing_organization": None,
                "issue_date": None,
                "expiration_date": None,
                "credential_id": None,
                "credential_url": None
            }
            certifications.append(cert_entry)
        elif isinstance(cert, dict):
            cert_entry = {
                "name": safe_get(cert, 'name'),
                "issuing_organization": safe_get(cert, 'issuing_organization') or safe_get(cert, 'authority'),
                "issue_date": safe_get(cert, 'issue_date'),
                "expiration_date": safe_get(cert, 'expiration_date'),
                "credential_id": safe_get(cert, 'credential_id'),
                "credential_url": safe_get(cert, 'credential_url')
            }
            certifications.append(cert_entry)

    # Build the complete response
    response = {
        "personal_info": personal_info,
        "summary": safe_get(linkedin_profile, 'summary'),
        "education": education,
        "work_experience": work_experience,
        "roles": None,  # Not typically available in LinkedIn data
        "skills": skills,
        "soft_skills": None,  # Not typically available in LinkedIn data
        "certifications": certifications,
        "languages": languages,
        "projects": [],  # Could be extracted from additional sections if available
        "references": None  # Not available in LinkedIn data
    }

    return response


# Old endpoints removed - functionality consolidated into /linkedin/search and /linkedin/transform

# All old endpoints have been removed and consolidated into:
# - /linkedin/search (handles search, search-only, batch search, and test functionality)
# - /linkedin/transform (handles transformation with batch support)
#
# The simplified API now provides a clean interface with just two endpoints
# that cover all the functionality previously spread across multiple endpoints.
