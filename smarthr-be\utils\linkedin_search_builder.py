import re
import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from enum import Enum

from models.linkedin import LinkedInSearchFilters, LinkedInExperienceLevel
from models.linkedin_config import LinkedInIntegrationConfig

logger = logging.getLogger(__name__)


class SearchParameterType(str, Enum):
    """Types of search parameters for validation."""
    TEXT = "text"
    LOCATION = "location"
    COMPANY = "company"
    TITLE = "title"
    SKILL = "skill"
    EDUCATION = "education"
    NUMERIC = "numeric"
    ENUM = "enum"


@dataclass
class SearchParameterRule:
    """Validation rule for search parameters."""
    parameter_type: SearchParameterType
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    pattern: Optional[str] = None
    allowed_values: Optional[List[str]] = None
    required: bool = False
    sanitize: bool = True


class LinkedInSearchParameterValidator:
    """Validator for LinkedIn search parameters."""
    
    def __init__(self, config: Optional[LinkedInIntegrationConfig] = None):
        self.config = config
        self._initialize_validation_rules()
    
    def _initialize_validation_rules(self) -> None:
        """Initialize validation rules for different parameter types."""
        self.validation_rules = {
            "keywords": SearchParameterRule(
                parameter_type=SearchParameterType.TEXT,
                min_length=2,
                max_length=200,
                sanitize=True
            ),
            "first_name": SearchParameterRule(
                parameter_type=SearchParameterType.TEXT,
                min_length=1,
                max_length=50,
                pattern=r"^[a-zA-Z\s\-'\.]+$",
                sanitize=True
            ),
            "last_name": SearchParameterRule(
                parameter_type=SearchParameterType.TEXT,
                min_length=1,
                max_length=50,
                pattern=r"^[a-zA-Z\s\-'\.]+$",
                sanitize=True
            ),
            "full_name": SearchParameterRule(
                parameter_type=SearchParameterType.TEXT,
                min_length=2,
                max_length=100,
                pattern=r"^[a-zA-Z\s\-'\.]+$",
                sanitize=True
            ),


            "location": SearchParameterRule(
                parameter_type=SearchParameterType.LOCATION,
                min_length=2,
                max_length=100,
                sanitize=True
            ),
            "country": SearchParameterRule(
                parameter_type=SearchParameterType.LOCATION,
                min_length=2,
                max_length=50,
                pattern=r"^[a-zA-Z\s]+$",
                sanitize=True
            ),
            "city": SearchParameterRule(
                parameter_type=SearchParameterType.LOCATION,
                min_length=2,
                max_length=50,
                sanitize=True
            ),

            "experience_level": SearchParameterRule(
                parameter_type=SearchParameterType.ENUM,
                allowed_values=[level.value for level in LinkedInExperienceLevel],
                sanitize=False
            ),
            "years_of_experience": SearchParameterRule(
                parameter_type=SearchParameterType.NUMERIC,
                sanitize=False
            ),
            "school": SearchParameterRule(
                parameter_type=SearchParameterType.EDUCATION,
                min_length=2,
                max_length=100,
                sanitize=True
            ),
            "degree": SearchParameterRule(
                parameter_type=SearchParameterType.EDUCATION,
                min_length=2,
                max_length=100,
                sanitize=True
            ),
            "limit": SearchParameterRule(
                parameter_type=SearchParameterType.NUMERIC,
                sanitize=False
            ),
            "offset": SearchParameterRule(
                parameter_type=SearchParameterType.NUMERIC,
                sanitize=False
            )
        }
    
    def validate_search_filters(self, filters: LinkedInSearchFilters) -> Tuple[bool, List[str]]:
        """Validate all search filters and return validation result."""
        errors = []
        
        try:
            # Convert filters to dict for validation
            filter_dict = filters.dict(exclude_none=True)
            
            # Validate each parameter
            for param_name, param_value in filter_dict.items():
                if param_name in self.validation_rules:
                    param_errors = self._validate_parameter(param_name, param_value)
                    errors.extend(param_errors)
            
            # Check for at least one search parameter
            search_params = [
                filters.keywords, filters.first_name, filters.last_name, filters.full_name,
                filters.location
            ]
            
            if not any(param for param in search_params if param):
                errors.append("At least one search parameter must be provided")
            
            # Validate limit ranges
            if filters.limit and (filters.limit < 1 or filters.limit > 100):
                errors.append("Limit must be between 1 and 100")
            
            if filters.offset and filters.offset < 0:
                errors.append("Offset must be non-negative")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"Search filter validation failed: {str(e)}")
            return False, [f"Validation error: {str(e)}"]
    
    def _validate_parameter(self, param_name: str, param_value: Any) -> List[str]:
        """Validate a single parameter."""
        errors = []
        rule = self.validation_rules[param_name]
        
        try:
            # Handle different parameter types
            if rule.parameter_type == SearchParameterType.TEXT:
                errors.extend(self._validate_text_parameter(param_name, param_value, rule))
            elif rule.parameter_type == SearchParameterType.LOCATION:
                errors.extend(self._validate_location_parameter(param_name, param_value, rule))
            elif rule.parameter_type == SearchParameterType.COMPANY:
                errors.extend(self._validate_company_parameter(param_name, param_value, rule))
            elif rule.parameter_type == SearchParameterType.TITLE:
                errors.extend(self._validate_title_parameter(param_name, param_value, rule))
            elif rule.parameter_type == SearchParameterType.NUMERIC:
                errors.extend(self._validate_numeric_parameter(param_name, param_value, rule))
            elif rule.parameter_type == SearchParameterType.ENUM:
                errors.extend(self._validate_enum_parameter(param_name, param_value, rule))
            elif rule.parameter_type == SearchParameterType.SKILL:
                errors.extend(self._validate_skill_parameter(param_name, param_value, rule))
            elif rule.parameter_type == SearchParameterType.EDUCATION:
                errors.extend(self._validate_education_parameter(param_name, param_value, rule))
            
        except Exception as e:
            errors.append(f"Parameter {param_name} validation failed: {str(e)}")
        
        return errors
    
    def _validate_text_parameter(self, param_name: str, param_value: Any, rule: SearchParameterRule) -> List[str]:
        """Validate text parameters."""
        errors = []

        # Special handling for keywords which can be a list
        if param_name == "keywords":
            if isinstance(param_value, list):
                if not param_value:
                    errors.append("keywords list cannot be empty")
                    return errors

                # Validate each keyword in the list
                for i, keyword in enumerate(param_value):
                    if not isinstance(keyword, str):
                        errors.append(f"keywords[{i}] must be a string")
                        continue

                    # Length validation for individual keywords
                    if rule.min_length and len(keyword) < rule.min_length:
                        errors.append(f"keywords[{i}] must be at least {rule.min_length} characters")

                    if rule.max_length and len(keyword) > rule.max_length:
                        errors.append(f"keywords[{i}] must be at most {rule.max_length} characters")

                    # Pattern validation for individual keywords
                    if rule.pattern and not re.match(rule.pattern, keyword):
                        errors.append(f"keywords[{i}] contains invalid characters")

                return errors
            else:
                errors.append("keywords must be a list of strings")
                return errors

        # Standard string validation for other text parameters
        if not isinstance(param_value, str):
            errors.append(f"{param_name} must be a string")
            return errors

        # Length validation
        if rule.min_length and len(param_value) < rule.min_length:
            errors.append(f"{param_name} must be at least {rule.min_length} characters")

        if rule.max_length and len(param_value) > rule.max_length:
            errors.append(f"{param_name} must be at most {rule.max_length} characters")

        # Pattern validation
        if rule.pattern and not re.match(rule.pattern, param_value):
            errors.append(f"{param_name} contains invalid characters")

        return errors
    
    def _validate_location_parameter(self, param_name: str, param_value: Any, rule: SearchParameterRule) -> List[str]:
        """Validate location parameters."""
        errors = self._validate_text_parameter(param_name, param_value, rule)
        
        # Additional location-specific validation
        if isinstance(param_value, str):
            # Check for common location formats
            if param_name == "country" and len(param_value) == 2:
                # Assume it's a country code
                if not param_value.isupper():
                    errors.append(f"{param_name} country code should be uppercase")
        
        return errors
    
    def _validate_company_parameter(self, param_name: str, param_value: Any, rule: SearchParameterRule) -> List[str]:
        """Validate company parameters."""
        return self._validate_text_parameter(param_name, param_value, rule)
    
    def _validate_title_parameter(self, param_name: str, param_value: Any, rule: SearchParameterRule) -> List[str]:
        """Validate job title parameters."""
        return self._validate_text_parameter(param_name, param_value, rule)
    
    def _validate_numeric_parameter(self, param_name: str, param_value: Any, rule: SearchParameterRule) -> List[str]:
        """Validate numeric parameters."""
        errors = []
        
        if not isinstance(param_value, (int, float)):
            errors.append(f"{param_name} must be a number")
            return errors
        
        # Specific numeric validations
        if param_name == "years_of_experience" and param_value < 0:
            errors.append("Years of experience cannot be negative")
        
        if param_name == "years_of_experience" and param_value > 50:
            errors.append("Years of experience seems unrealistic (>50)")
        
        return errors
    
    def _validate_enum_parameter(self, param_name: str, param_value: Any, rule: SearchParameterRule) -> List[str]:
        """Validate enum parameters."""
        errors = []
        
        if rule.allowed_values and param_value not in rule.allowed_values:
            errors.append(f"{param_name} must be one of: {', '.join(rule.allowed_values)}")
        
        return errors
    
    def _validate_skill_parameter(self, param_name: str, param_value: Any, rule: SearchParameterRule) -> List[str]:
        """Validate skill parameters."""
        errors = []
        
        if isinstance(param_value, list):
            for skill in param_value:
                if not isinstance(skill, str):
                    errors.append(f"All skills must be strings")
                elif len(skill) < 2:
                    errors.append(f"Skill names must be at least 2 characters")
                elif len(skill) > 50:
                    errors.append(f"Skill names must be at most 50 characters")
        else:
            errors.append(f"{param_name} must be a list of strings")
        
        return errors
    
    def _validate_education_parameter(self, param_name: str, param_value: Any, rule: SearchParameterRule) -> List[str]:
        """Validate education parameters."""
        return self._validate_text_parameter(param_name, param_value, rule)


class LinkedInSearchParameterSanitizer:
    """Sanitizer for LinkedIn search parameters."""
    
    @staticmethod
    def sanitize_search_filters(filters: LinkedInSearchFilters) -> LinkedInSearchFilters:
        """Sanitize all search filters."""
        sanitized_data = {}
        
        for field_name, field_value in filters.dict(exclude_none=True).items():
            if field_value is not None:
                sanitized_data[field_name] = LinkedInSearchParameterSanitizer._sanitize_parameter(
                    field_name, field_value
                )
        
        return LinkedInSearchFilters(**sanitized_data)
    
    @staticmethod
    def _sanitize_parameter(param_name: str, param_value: Any) -> Any:
        """Sanitize a single parameter."""
        if isinstance(param_value, str):
            # Remove extra whitespace
            param_value = param_value.strip()
            
            # Remove multiple spaces
            param_value = re.sub(r'\s+', ' ', param_value)
            
            # Remove potentially dangerous characters for certain fields
            if param_name in ['first_name', 'last_name', 'full_name']:
                # Keep only letters, spaces, hyphens, apostrophes, and dots
                param_value = re.sub(r"[^a-zA-Z\s\-'\.]+", '', param_value)
            
            # Escape special characters for search
            if param_name in ['keywords']:
                # Remove or escape special LinkedIn search characters
                param_value = param_value.replace('"', '\\"')
                param_value = param_value.replace('(', '\\(')
                param_value = param_value.replace(')', '\\)')
        
        elif isinstance(param_value, list):
            # Sanitize list items
            param_value = [
                LinkedInSearchParameterSanitizer._sanitize_parameter(param_name, item)
                for item in param_value
            ]
        
        return param_value


class LinkedInSearchQueryBuilder:
    """Builder for optimized LinkedIn search queries."""

    def __init__(self, config: Optional[LinkedInIntegrationConfig] = None):
        self.config = config

    def build_search_query(self, filters: LinkedInSearchFilters) -> str:
        """Build optimized search query from filters."""
        query_parts = []

        # Handle name searches
        if filters.full_name:
            query_parts.append(f'"{filters.full_name}"')
        elif filters.first_name and filters.last_name:
            query_parts.append(f'"{filters.first_name} {filters.last_name}"')
        elif filters.first_name:
            query_parts.append(f'"{filters.first_name}"')
        elif filters.last_name:
            query_parts.append(f'"{filters.last_name}"')



        # Handle skills
        if filters.skills:
            skills_query = " OR ".join([f'"{skill}"' for skill in filters.skills[:5]])  # Limit to 5 skills
            query_parts.append(f"({skills_query})")

        # Handle keywords
        if filters.keywords:
            # Join keywords with OR logic for broader search
            keywords_query = " OR ".join([f'"{keyword}"' for keyword in filters.keywords])
            query_parts.append(f"({keywords_query})")

        # Combine query parts
        if query_parts:
            return " AND ".join(query_parts)
        else:
            return "*"  # Fallback to wildcard search

    def build_advanced_filters(self, filters: LinkedInSearchFilters) -> Dict[str, Any]:
        """Build advanced filter parameters for LinkedIn search."""
        advanced_filters = {}

        # Location filters
        if filters.location:
            advanced_filters["geoUrn"] = self._encode_location(filters.location)
        elif filters.country:
            advanced_filters["geoUrn"] = self._encode_location(filters.country)
        elif filters.city:
            advanced_filters["geoUrn"] = self._encode_location(filters.city)



        # Experience level filter
        if filters.experience_level:
            advanced_filters["experienceLevel"] = filters.experience_level

        # Education filters
        if filters.school:
            advanced_filters["school"] = self._encode_school(filters.school)

        if filters.degree:
            advanced_filters["fieldOfStudy"] = filters.degree

        # Connection filters
        if filters.connections_of:
            advanced_filters["connectionOf"] = filters.connections_of

        return advanced_filters

    def _encode_location(self, location: str) -> str:
        """Encode location for LinkedIn search."""
        # This would typically involve mapping location names to LinkedIn geo URNs
        # For now, return the location as-is
        return location



    def _encode_school(self, school: str) -> str:
        """Encode school for LinkedIn search."""
        # This would typically involve mapping school names to LinkedIn school URNs
        return school

    def optimize_search_parameters(self, filters: LinkedInSearchFilters) -> LinkedInSearchFilters:
        """Optimize search parameters for better results."""
        optimized_filters = filters.copy()

        # Optimize limit based on search complexity
        search_complexity = self._calculate_search_complexity(filters)
        if search_complexity > 0.7 and filters.limit > 50:
            optimized_filters.limit = 50  # Reduce limit for complex searches

        # Optimize keywords
        if filters.keywords:
            optimized_filters.keywords = self._optimize_keywords(filters.keywords)

        return optimized_filters

    def _calculate_search_complexity(self, filters: LinkedInSearchFilters) -> float:
        """Calculate search complexity score (0-1)."""
        complexity_factors = 0
        total_factors = 0

        # Count active filters
        active_filters = sum([
            bool(filters.keywords),
            bool(filters.first_name or filters.last_name or filters.full_name),
            bool(filters.location or filters.country or filters.city),
            bool(filters.skills),
            bool(filters.school or filters.degree)
        ])

        complexity_factors += min(active_filters / 8.0, 1.0)  # Normalize to 0-1
        total_factors += 1

        # Consider skill count
        if filters.skills:
            skill_complexity = min(len(filters.skills) / 10.0, 1.0)
            complexity_factors += skill_complexity
            total_factors += 1

        return complexity_factors / total_factors if total_factors > 0 else 0.0

    def _optimize_keywords(self, keywords: List[str]) -> List[str]:
        """Optimize keyword list for better search results."""
        # Remove common stop words that don't add value to LinkedIn searches
        stop_words = {'and', 'or', 'the', 'a', 'an', 'in', 'at', 'for', 'with', 'by'}

        optimized_keywords = []
        for keyword in keywords:
            words = keyword.split()
            optimized_words = [word for word in words if word.lower() not in stop_words]
            if optimized_words:
                optimized_keywords.append(' '.join(optimized_words))
            else:
                optimized_keywords.append(keyword)  # Keep original if all words were stop words

        return optimized_keywords


# Convenience functions
def validate_linkedin_search_filters(filters: LinkedInSearchFilters,
                                   config: Optional[LinkedInIntegrationConfig] = None) -> Tuple[bool, List[str]]:
    """Validate LinkedIn search filters."""
    validator = LinkedInSearchParameterValidator(config)
    return validator.validate_search_filters(filters)


def sanitize_linkedin_search_filters(filters: LinkedInSearchFilters) -> LinkedInSearchFilters:
    """Sanitize LinkedIn search filters."""
    return LinkedInSearchParameterSanitizer.sanitize_search_filters(filters)


def build_optimized_linkedin_search(filters: LinkedInSearchFilters,
                                  config: Optional[LinkedInIntegrationConfig] = None) -> Tuple[str, Dict[str, Any], LinkedInSearchFilters]:
    """Build optimized LinkedIn search query and filters."""
    builder = LinkedInSearchQueryBuilder(config)

    # Sanitize and optimize filters
    sanitized_filters = sanitize_linkedin_search_filters(filters)
    optimized_filters = builder.optimize_search_parameters(sanitized_filters)

    # Build query and advanced filters
    search_query = builder.build_search_query(optimized_filters)
    advanced_filters = builder.build_advanced_filters(optimized_filters)

    return search_query, advanced_filters, optimized_filters
